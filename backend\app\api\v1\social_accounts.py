import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from datetime import datetime
import csv
import io
from bson import ObjectId

from app.core.security import get_current_user
from app.core.schemas.social_repository import SocialDatabaseService
from app.api.social import get_social_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/social",
    tags=["social"],
    dependencies=[Depends(get_current_user)]
)

# 定义数据模型
class SocialAccount(BaseModel):
    id: Optional[str] = None
    username: str
    password: Optional[str] = None
    recovery_email: Optional[str] = None
    recovery_code: Optional[str] = None
    display_name: Optional[str] = None
    platform_id: str
    core_service_id: str
    status: Optional[str] = "active"
    avatar: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    last_updated_date: Optional[str] = None  # 账号最后更新日期，用于过期提醒

# 创建账号时使用的模型（不包含id字段）
class CreateSocialAccount(BaseModel):
    username: str
    password: Optional[str] = None
    recovery_email: Optional[str] = None
    recovery_code: Optional[str] = None
    display_name: Optional[str] = None
    platform_id: str
    core_service_id: str
    status: Optional[str] = "active"
    avatar: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    last_updated_date: Optional[str] = None  # 账号最后更新日期，用于过期提醒

class AccountImportOptions(BaseModel):
    platform_mapping: Dict[str, str]
    core_service_id: str
    conflict_strategy: str = "update"

# API端点：获取账号列表
@router.get("/accounts")
async def get_social_accounts(
    platform_id: Optional[str] = None,
    core_service_id: Optional[str] = None,
    status: Optional[str] = None,
    keyword: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取社交媒体账号列表，支持按平台和Core服务筛选

    - **platform_id**: 可选，平台ID筛选
    - **core_service_id**: 可选，Core服务ID筛选
    - **status**: 可选，账号状态筛选
    - **keyword**: 可选，关键词搜索（支持账号名称、用户名、显示名称、备注等字段的模糊搜索）
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    """
    try:
        logger.info(f"获取社交账号列表: platform_id={platform_id}, core_service_id={core_service_id}, status={status}, keyword={keyword}")

        # 构建查询条件
        query = {}
        if platform_id:
            # 处理平台ID，支持多种格式
            actual_platform_id = platform_id
            try:
                logger.info(f"处理平台ID: {platform_id}, 长度: {len(platform_id)}")

                # 如果传入的是24位十六进制字符串（MongoDB ObjectId格式）
                if len(platform_id) == 24 and all(c in '0123456789abcdefABCDEF' for c in platform_id):
                    # 直接使用，这已经是MongoDB ObjectId字符串格式
                    actual_platform_id = platform_id
                    logger.info(f"使用MongoDB ObjectId格式的平台ID: {actual_platform_id}")
                else:
                    # 如果传入的是平台的id字段（如'youtube'），需要查找对应的MongoDB _id
                    logger.info(f"查找平台ID '{platform_id}' 对应的MongoDB _id")
                    platform = await db_service.db.social_platforms.find_one({"id": platform_id})
                    if platform and '_id' in platform:
                        actual_platform_id = str(platform['_id'])
                        logger.info(f"将平台ID '{platform_id}' 转换为MongoDB _id: {actual_platform_id}")
                    else:
                        logger.warning(f"未找到平台ID '{platform_id}' 对应的平台，使用原始值")
                        actual_platform_id = platform_id

                query["platform_id"] = actual_platform_id
                logger.info(f"最终使用的平台筛选条件: platform_id={actual_platform_id}")
            except Exception as e:
                logger.error(f"处理平台ID失败: {str(e)}")
                query["platform_id"] = platform_id
                logger.info(f"异常情况下使用原始平台ID: platform_id={platform_id}")

        if core_service_id:
            query["core_service_id"] = core_service_id
            logger.info(f"添加Core服务筛选条件: core_service_id={core_service_id}")
        if status:
            query["status"] = status
            logger.info(f"添加状态筛选条件: status={status}")

        # 添加关键词搜索条件
        if keyword:
            # 支持多字段模糊搜索
            search_conditions = [
                {"username": {"$regex": keyword, "$options": "i"}},
                {"display_name": {"$regex": keyword, "$options": "i"}},
                {"account_name": {"$regex": keyword, "$options": "i"}},
                {"remarks": {"$regex": keyword, "$options": "i"}},
                {"id": {"$regex": keyword, "$options": "i"}},
                {"email": {"$regex": keyword, "$options": "i"}},
                {"phone": {"$regex": keyword, "$options": "i"}}
            ]

            # 如果已经有其他查询条件，使用$and组合
            if query:
                query = {"$and": [query, {"$or": search_conditions}]}
            else:
                query = {"$or": search_conditions}

            logger.info(f"添加关键词搜索条件: keyword={keyword}")

        logger.info(f"最终查询条件: {query}")

        # 首先尝试查找已关联的账号
        if platform_id and core_service_id:
            try:
                logger.info("尝试从device_account_mappings查找已关联的账号")

                # 查询设备账号映射
                mapping_query = {
                    "platform_id": platform_id,
                    "core_service_id": core_service_id,
                    "status": "active"
                }

                logger.info(f"映射查询条件: {mapping_query}")

                # 直接查询数据库集合
                try:
                    cursor = db_service.db.device_account_mappings.find(mapping_query)
                    mappings = await cursor.to_list(length=None)
                    logger.info(f"找到 {len(mappings)} 个设备账号映射")

                    # 打印每个映射的详细信息
                    for i, mapping in enumerate(mappings):
                        logger.info(f"映射 {i+1}: {mapping}")
                except Exception as e:
                    logger.error(f"查询设备账号映射失败: {str(e)}")
                    mappings = []

                if mappings:
                    # 收集所有关联的账号ID
                    account_ids = []
                    for mapping in mappings:
                        account_id = mapping.get("account_id")
                        if account_id:
                            logger.info(f"处理账号ID: {account_id}, 类型: {type(account_id)}")

                            # 如果是字符串但看起来像ObjectId，尝试转换
                            if isinstance(account_id, str) and len(account_id) == 24:
                                try:
                                    account_id = ObjectId(account_id)
                                    logger.info(f"将字符串ID转换为ObjectId: {account_id}")
                                except Exception as e:
                                    logger.warning(f"转换ObjectId失败: {str(e)}")

                            # 添加到账号ID列表
                            account_ids.append(account_id)

                    logger.info(f"关联的账号ID列表: {account_ids}")

                    # 查询这些账号
                    if account_ids:
                        accounts = []

                        # 首先尝试使用$in查询一次性获取所有账号
                        try:
                            # 构建查询条件
                            id_query = {"$or": []}

                            # 添加_id条件
                            id_list = []
                            for aid in account_ids:
                                if isinstance(aid, str) and len(aid) == 24:
                                    try:
                                        id_list.append(ObjectId(aid))
                                    except:
                                        id_list.append(aid)
                                else:
                                    id_list.append(aid)

                            if id_list:
                                id_query["$or"].append({"_id": {"$in": id_list}})

                            # 添加id条件
                            if account_ids:
                                id_query["$or"].append({"id": {"$in": account_ids}})

                            logger.info(f"账号查询条件: {id_query}")

                            # 执行查询
                            if id_query["$or"]:
                                cursor = db_service.db.social_accounts.find(id_query)
                                bulk_accounts = await cursor.to_list(length=None)
                                logger.info(f"批量查询到 {len(bulk_accounts)} 个账号")
                                accounts.extend(bulk_accounts)
                        except Exception as e:
                            logger.error(f"批量查询账号失败: {str(e)}")

                        # 如果批量查询失败或没有结果，尝试逐个查询
                        if not accounts:
                            logger.info("批量查询失败或无结果，尝试逐个查询账号")
                            for account_id in account_ids:
                                try:
                                    # 尝试通过_id查询
                                    account = None

                                    # 尝试直接使用字符串ID查询
                                    if isinstance(account_id, str):
                                        # 首先尝试直接使用字符串ID查询
                                        account = await db_service.db.social_accounts.find_one({"_id": account_id})
                                        logger.info(f"直接使用字符串_id={account_id}查询账号: {account is not None}")

                                        # 如果没找到，尝试转换为ObjectId再查询
                                        if not account and len(account_id) == 24:
                                            try:
                                                account_id_obj = ObjectId(account_id)
                                                account = await db_service.db.social_accounts.find_one({"_id": account_id_obj})
                                                logger.info(f"转换为ObjectId后查询账号: {account is not None}")
                                            except Exception as e:
                                                logger.warning(f"转换ObjectId失败: {str(e)}")

                                        # 如果还没找到，尝试通过id字段查询
                                        if not account:
                                            account = await db_service.db.social_accounts.find_one({"id": account_id})
                                            logger.info(f"通过id字段查询账号: {account is not None}")

                                    # 如果是ObjectId类型
                                    elif isinstance(account_id, ObjectId):
                                        account = await db_service.db.social_accounts.find_one({"_id": account_id})
                                        logger.info(f"通过ObjectId _id={account_id}查询账号: {account is not None}")

                                    if account:
                                        accounts.append(account)
                                    else:
                                        logger.warning(f"未找到账号，ID: {account_id}")
                                except Exception as e:
                                    logger.warning(f"获取账号失败，ID: {account_id}, 错误: {str(e)}")

                        logger.info(f"从映射中找到 {len(accounts)} 个账号")

                        # 只有在找到账号的情况下才返回，否则继续执行回退逻辑
                        if accounts:
                            # 格式化返回数据并添加平台信息
                            formatted_accounts = []
                            for account in accounts:
                                # 🔧 修复：优先使用账号自带的id字段，如果没有则使用_id
                                if 'id' not in account and '_id' in account:
                                    account['id'] = str(account['_id'])

                                # 保留_id字段作为数据库ID，但转换为字符串
                                if '_id' in account:
                                    account['_id'] = str(account['_id'])

                                # 添加平台信息
                                if 'platform_id' in account and account['platform_id']:
                                    try:
                                        # 查询平台信息
                                        platform_query = {}
                                        platform_id = account['platform_id']

                                        # 判断platform_id是ObjectId还是字符串
                                        if isinstance(platform_id, str) and len(platform_id) == 24:
                                            # 这是ObjectId字符串，尝试转换
                                            try:
                                                from bson import ObjectId
                                                platform_query = {"_id": ObjectId(platform_id)}
                                            except:
                                                platform_query = {"_id": platform_id}
                                        else:
                                            # 这是平台标识符
                                            platform_query = {"id": platform_id}

                                        platform = await db_service.db.social_platforms.find_one(platform_query)
                                        if platform:
                                            account['platform_name'] = platform.get('name', platform.get('id', '未知平台'))
                                            account['platform_display_name'] = platform.get('name', platform.get('id', '未知平台'))
                                        else:
                                            logger.warning(f"未找到平台信息: {platform_id}")
                                            account['platform_name'] = '未知平台'
                                            account['platform_display_name'] = '未知平台'
                                    except Exception as e:
                                        logger.warning(f"获取平台信息失败: {str(e)}")
                                        account['platform_name'] = '未知平台'
                                        account['platform_display_name'] = '未知平台'

                                formatted_accounts.append(account)

                            # 返回包含总记录数的响应
                            return {
                                "data": formatted_accounts,
                                "total": len(formatted_accounts),
                                "page": 1,
                                "page_size": len(formatted_accounts)
                            }
                        else:
                            logger.info("从映射中未找到账号，将继续执行直接查询逻辑")
            except Exception as mapping_error:
                logger.error(f"查询设备账号映射失败: {str(mapping_error)}")

        # 如果没有找到映射或者查询映射失败，尝试直接查询账号
        try:
            # 查询账号
            accounts = await db_service.get_accounts_by_query(query, skip=skip, limit=limit)
            logger.info(f"从数据库查询到 {len(accounts)} 个账号")

            # 获取总记录数
            total_count = await db_service.count_accounts(query)
            logger.info(f"符合条件的总记录数: {total_count}")
        except Exception as query_error:
            logger.error(f"查询账号失败: {str(query_error)}")
            # 尝试直接从集合中查询
            try:
                collections = db_service.db.list_collection_names()
                logger.info(f"数据库集合列表: {collections}")

                if 'social_accounts' in collections:
                    cursor = db_service.db.social_accounts.find(query).skip(skip).limit(limit)
                    accounts = await cursor.to_list(length=limit)
                    logger.info(f"直接从social_accounts集合中找到 {len(accounts)} 个账号")

                    # 获取总记录数
                    total_count = await db_service.db.social_accounts.count_documents(query)
                    logger.info(f"符合条件的总记录数: {total_count}")
                else:
                    accounts = []
                    total_count = 0
            except Exception as direct_query_error:
                logger.error(f"直接查询账号失败: {str(direct_query_error)}")
                accounts = []
                total_count = 0

        # 格式化返回数据并添加平台信息
        formatted_accounts = []
        for account in accounts:
            # 🔧 修复：优先使用账号自带的id字段，如果没有则使用_id
            if 'id' not in account and '_id' in account:
                account['id'] = str(account['_id'])

            # 保留_id字段作为数据库ID，但转换为字符串
            if '_id' in account:
                account['_id'] = str(account['_id'])

            # 添加平台信息
            if 'platform_id' in account and account['platform_id']:
                try:
                    # 查询平台信息
                    platform_query = {}
                    platform_id = account['platform_id']

                    # 判断platform_id是ObjectId还是字符串
                    if isinstance(platform_id, str) and len(platform_id) == 24:
                        # 这是ObjectId字符串，尝试转换
                        try:
                            from bson import ObjectId
                            platform_query = {"_id": ObjectId(platform_id)}
                        except:
                            platform_query = {"_id": platform_id}
                    else:
                        # 这是平台标识符
                        platform_query = {"id": platform_id}

                    platform = await db_service.db.social_platforms.find_one(platform_query)
                    if platform:
                        account['platform_name'] = platform.get('name', platform.get('id', '未知平台'))
                        account['platform_display_name'] = platform.get('name', platform.get('id', '未知平台'))
                    else:
                        logger.warning(f"未找到平台信息: {platform_id}")
                        account['platform_name'] = '未知平台'
                        account['platform_display_name'] = '未知平台'
                except Exception as e:
                    logger.warning(f"获取平台信息失败: {str(e)}")
                    account['platform_name'] = '未知平台'
                    account['platform_display_name'] = '未知平台'

            formatted_accounts.append(account)

        logger.info(f"最终找到 {len(formatted_accounts)} 个账号")

        # 返回包含总记录数的响应
        return {
            "data": formatted_accounts,
            "total": total_count,
            "page": skip // limit + 1 if limit > 0 else 1,
            "page_size": limit
        }

    except Exception as e:
        logger.error(f"获取社交账号列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取社交账号列表失败: {str(e)}"
        )

# API端点：创建账号
@router.post("/accounts", response_model=Dict[str, Any])
async def create_social_account(
    account: CreateSocialAccount,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    创建社交媒体账号

    - **account**: 账号信息
    """
    try:
        logger.info(f"创建社交账号: {account.model_dump()}")

        # 获取账号数据
        account_data = account.model_dump()

        # 生成唯一的账号ID
        import time
        unique_id = f"{account_data['platform_id']}_{account_data['username']}_{int(time.time())}"
        account_data["id"] = unique_id
        logger.info(f"生成唯一账号ID: {unique_id}")

        # 添加创建和更新时间
        now = datetime.now()
        account_data["created_at"] = now
        account_data["updated_at"] = now

        account_id = await db_service.create_account(account_data)

        # 获取创建的账号
        created_account = await db_service.get_account(account_id)

        # 格式化返回数据
        if '_id' in created_account:
            created_account['id'] = str(created_account['_id'])
            del created_account['_id']

        logger.info(f"账号创建成功: {created_account}")
        return created_account

    except Exception as e:
        logger.error(f"创建社交账号失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"创建社交账号失败: {str(e)}"
        )

# API端点：更新账号
@router.put("/accounts/{account_id}", response_model=Dict[str, Any])
async def update_social_account(
    account_id: str,
    account: SocialAccount,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    更新社交媒体账号

    - **account_id**: 账号ID
    - **account**: 更新的账号信息
    """
    try:
        logger.info(f"更新社交账号 {account_id}: {account.model_dump()}")

        # 检查账号是否存在
        existing_account = await db_service.get_account(account_id)
        if not existing_account:
            raise HTTPException(status_code=404, detail="账号不存在")

        # 更新账号
        update_data = account.model_dump(exclude={"id"}, exclude_unset=True)

        # 更新时间
        update_data["updated_at"] = datetime.now()

        result = await db_service.update_account(account_id, update_data)
        if not result:
            raise HTTPException(status_code=500, detail="更新账号失败")

        # 获取更新后的账号
        updated_account = await db_service.get_account(account_id)

        # 格式化返回数据
        if '_id' in updated_account:
            updated_account['id'] = str(updated_account['_id'])
            del updated_account['_id']

        logger.info(f"账号更新成功: {updated_account}")
        return updated_account
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新社交媒体账号失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"更新社交媒体账号失败: {str(e)}"
        )

# API端点：复制账号
@router.post("/accounts/{account_id}/copy", response_model=Dict[str, Any])
async def copy_social_account(
    account_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    复制社交媒体账号

    - **account_id**: 要复制的账号ID
    """
    try:
        logger.info(f"复制社交账号: {account_id}")

        # 检查原账号是否存在
        original_account = await db_service.get_account(account_id)
        if not original_account:
            raise HTTPException(status_code=404, detail="原账号不存在")

        # 准备复制的账号数据
        copy_data = original_account.copy()

        # 移除不需要复制的字段
        fields_to_remove = ['_id', 'id', 'created_at', 'updated_at']
        for field in fields_to_remove:
            copy_data.pop(field, None)

        # 修改用户名以区分复制的账号，确保用户名唯一
        original_username = copy_data.get('username', '')
        platform_id = copy_data.get('platform_id', '')

        # 生成唯一的用户名和ID
        import time
        timestamp = int(time.time())
        copy_data['username'] = f"{original_username}_copy_{timestamp}"

        # 生成唯一的ID字段，避免重复键错误
        copy_data['id'] = f"{platform_id}_{original_username}_copy_{timestamp}"

        # 修改显示名称
        original_display_name = copy_data.get('display_name', '')
        if original_display_name:
            copy_data['display_name'] = f"{original_display_name} (副本)"
        else:
            copy_data['display_name'] = f"{original_username} (副本)"

        # 添加创建和更新时间
        now = datetime.now()
        copy_data["created_at"] = now
        copy_data["updated_at"] = now

        # 创建复制的账号
        new_account_id = await db_service.create_account(copy_data)

        # 获取创建的账号
        copied_account = await db_service.get_account(new_account_id)

        # 格式化返回数据
        if '_id' in copied_account:
            copied_account['id'] = str(copied_account['_id'])
            del copied_account['_id']

        logger.info(f"账号复制成功: {copied_account}")
        return copied_account
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"复制社交媒体账号失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"复制社交媒体账号失败: {str(e)}"
        )

# API端点：删除账号
@router.delete("/accounts/{account_id}", response_model=Dict[str, bool])
async def delete_social_account(
    account_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    删除社交媒体账号

    - **account_id**: 账号ID
    """
    try:
        logger.info(f"删除社交账号: {account_id}")

        # 检查账号是否存在
        existing_account = await db_service.get_account(account_id)
        if not existing_account:
            raise HTTPException(status_code=404, detail="账号不存在")

        # 删除账号
        result = await db_service.delete_account(account_id)
        if not result:
            raise HTTPException(status_code=500, detail="删除账号失败")

        logger.info(f"账号删除成功: {account_id}")
        return {"deleted": True}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除社交媒体账号失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"删除社交媒体账号失败: {str(e)}"
        )

# API端点：批量更新账号
@router.post("/accounts/batch_update", response_model=Dict[str, int])
async def batch_update_accounts(
    data: Dict[str, Any],
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    批量更新社交媒体账号

    - **account_ids**: 账号ID列表
    - **update_data**: 更新数据
    """
    try:
        account_ids = data.get("account_ids", [])
        update_data = data.get("update_data", {})

        if not account_ids or not update_data:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        logger.info(f"批量更新账号: {account_ids}")

        # 更新时间
        update_data["updated_at"] = datetime.now()

        # 批量更新账号
        count = await db_service.batch_update_accounts(account_ids, update_data)

        logger.info(f"批量更新成功，更新了 {count} 个账号")
        return {"updated_count": count}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量更新社交媒体账号失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量更新社交媒体账号失败: {str(e)}"
        )

# API端点：批量删除账号
@router.post("/accounts/batch_delete", response_model=Dict[str, int])
async def batch_delete_accounts(
    data: Dict[str, List[str]],
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    批量删除社交媒体账号

    - **account_ids**: 账号ID列表
    """
    try:
        account_ids = data.get("account_ids", [])

        if not account_ids:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        logger.info(f"批量删除账号: {account_ids}")

        # 批量删除账号
        count = await db_service.batch_delete_accounts(account_ids)

        logger.info(f"批量删除成功，删除了 {count} 个账号")
        return {"deleted_count": count}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除社交媒体账号失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量删除社交媒体账号失败: {str(e)}"
        )

# API端点：导入账号
@router.post("/accounts/import", response_model=Dict[str, Any])
async def import_social_accounts(
    import_data: Dict[str, Any],
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    导入社交媒体账号

    - **text_content**: 文本内容
    - **platform_mapping**: 平台映射，如 {"GG": "youtube", "FB": "facebook"}
    - **core_service_id**: Core服务ID
    - **format**: 导入格式，支持 "text"(默认) 或 "csv"
    """
    try:
        text_content = import_data.get("text_content", "")
        platform_mapping = import_data.get("platform_mapping", {
            "GG": "youtube",
            "FB": "facebook"
        })
        core_service_id = import_data.get("core_service_id", "default")
        import_format = import_data.get("format", "text")

        if not text_content:
            raise HTTPException(
                status_code=400,
                detail="文本内容不能为空"
            )

        logger.info(f"导入账号请求: format={import_format}, core_service_id={core_service_id}")

        # 根据格式选择不同的导入方法
        if import_format.lower() == "csv":
            # CSV格式导入

            # 检测并处理BOM标记
            if text_content.startswith('\ufeff'):
                # 如果有BOM标记，直接使用UTF-8编码
                logger.info("检测到UTF-8 BOM标记")
                csv_file = io.StringIO(text_content)
            else:
                # 尝试检测编码
                try:
                    # 尝试使用UTF-8解码
                    text_content.encode('utf-8').decode('utf-8')
                    csv_file = io.StringIO(text_content)
                    logger.info("使用UTF-8编码解析CSV")
                except UnicodeError:
                    # 如果UTF-8失败，尝试使用GBK
                    try:
                        # 将GBK编码的内容转换为UTF-8
                        decoded_content = text_content.encode('latin1').decode('gbk')
                        csv_file = io.StringIO(decoded_content)
                        logger.info("使用GBK编码解析CSV")
                    except UnicodeError:
                        # 如果GBK也失败，尝试使用UTF-16
                        try:
                            decoded_content = text_content.encode('latin1').decode('utf-16')
                            csv_file = io.StringIO(decoded_content)
                            logger.info("使用UTF-16编码解析CSV")
                        except UnicodeError:
                            # 如果所有尝试都失败，回退到原始内容
                            logger.warning("无法检测CSV编码，使用原始内容")
                            csv_file = io.StringIO(text_content)

            # 解析CSV内容
            reader = csv.DictReader(csv_file)

            # 准备账号数据
            accounts = []
            for row in reader:
                # 清理数据，移除空值
                account_data = {k: v for k, v in row.items() if v}

                # 确保必要字段存在
                if "username" not in account_data or "platform_id" not in account_data:
                    continue

                # 设置Core服务ID
                if "core_service_id" not in account_data:
                    account_data["core_service_id"] = core_service_id

                # 设置状态
                if "status" not in account_data:
                    account_data["status"] = "active"

                # 处理tags字段（如果存在）
                if "tags" in account_data and account_data["tags"]:
                    try:
                        # 尝试解析JSON字符串
                        import json
                        account_data["tags"] = json.loads(account_data["tags"])
                    except:
                        # 如果解析失败，尝试按逗号分割
                        account_data["tags"] = [tag.strip() for tag in account_data["tags"].split(",")]

                accounts.append(account_data)

            # 批量导入账号
            result = db_service.import_accounts_batch(accounts)
            return result
        else:
            # 传统文本格式导入
            result = db_service.import_accounts_from_text(
                text_content=text_content,
                platform_mapping=platform_mapping,
                core_service_id=core_service_id
            )
            return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导入账号失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"导入账号失败: {str(e)}"
        )

# API端点：导出账号 (GET方法 - 按筛选条件导出)
@router.get("/accounts/export")
async def export_social_accounts(
    platform_id: Optional[str] = None,
    core_service_id: Optional[str] = None,
    format: str = "csv",  # 默认为CSV格式
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    导出社交媒体账号 (GET方法 - 按筛选条件导出)

    - **platform_id**: 可选，平台ID筛选
    - **core_service_id**: 可选，Core服务ID筛选
    - **format**: 导出格式，支持 "csv" 或 "json"，默认为 "csv"
    """
    return await _export_accounts(
        platform_id=platform_id,
        core_service_id=core_service_id,
        format=format,
        account_ids=None,
        db_service=db_service
    )

# API端点：导出账号 (POST方法 - 导出指定ID的账号)
@router.post("/accounts/export")
async def export_social_accounts_by_ids(
    export_data: Dict[str, Any],
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    导出指定ID的社交媒体账号

    - **account_ids**: 必填，要导出的账号ID列表
    - **format**: 导出格式，支持 "csv" 或 "json"，默认为 "csv"
    """
    account_ids = export_data.get("account_ids", [])
    format = export_data.get("format", "csv")

    if not account_ids:
        raise HTTPException(
            status_code=400,
            detail="账号ID列表不能为空"
        )

    return await _export_accounts(
        platform_id=None,
        core_service_id=None,
        format=format,
        account_ids=account_ids,
        db_service=db_service
    )

# 通用导出账号函数
async def _export_accounts(
    platform_id: Optional[str] = None,
    core_service_id: Optional[str] = None,
    format: str = "csv",
    account_ids: Optional[List[str]] = None,
    db_service: SocialDatabaseService = None
):
    """
    通用导出账号函数，支持按筛选条件或指定ID导出

    - **platform_id**: 可选，平台ID筛选
    - **core_service_id**: 可选，Core服务ID筛选
    - **format**: 导出格式，支持 "csv" 或 "json"，默认为 "csv"
    - **account_ids**: 可选，要导出的账号ID列表
    """
    from fastapi.responses import Response
    import csv
    import io
    import json

    try:
        logger.info(f"导出账号请求: platform_id={platform_id}, core_service_id={core_service_id}, format={format}, account_ids={account_ids}")

        # 根据不同的导出方式获取账号数据
        if account_ids:
            # 按ID列表导出
            accounts = []
            for account_id in account_ids:
                try:
                    # 尝试查找账号
                    account = db_service.get_account_by_id(account_id)
                    if account:
                        accounts.append(account)
                except Exception as e:
                    logger.warning(f"获取账号失败，ID: {account_id}, 错误: {str(e)}")

            logger.info(f"按ID列表导出，找到 {len(accounts)} 个账号")
        else:
            # 按筛选条件导出
            query = {}
            if platform_id:
                query["platform_id"] = platform_id
            if core_service_id:
                query["core_service_id"] = core_service_id

            logger.info(f"查询条件: {query}")

            try:
                accounts = await db_service.get_accounts_by_query(query)
                logger.info(f"找到 {len(accounts)} 个账号")
            except Exception as query_error:
                logger.error(f"查询账号失败: {str(query_error)}")
                accounts = []

        # 🔧 修复：处理账号数据，确保ID字段正确
        for account in accounts:
            # 优先使用账号自带的id字段，如果没有则使用_id
            if 'id' not in account and '_id' in account:
                account['id'] = str(account['_id'])

            # 保留_id字段作为数据库ID，但转换为字符串
            if '_id' in account:
                account['_id'] = str(account['_id'])

        # 根据格式返回不同的响应
        if format.lower() == "csv":
            # 创建CSV输出
            output = io.StringIO()

            # 添加UTF-8 BOM标记，确保Excel等应用能正确识别中文
            output.write('\ufeff')

            # 定义人类习惯的表头顺序
            preferred_order = [
                "id", "username", "password", "display_name", "platform_id",
                "core_service_id", "status", "recovery_email", "recovery_code",
                "description", "tags", "created_at", "updated_at"
            ]

            # 收集所有账号中出现的所有字段
            all_fields = set()
            if accounts:
                for account in accounts:
                    all_fields.update(account.keys())

            # 按照优先顺序排列字段
            fieldnames = []

            # 首先添加优先顺序中存在的字段
            for field in preferred_order:
                if field in all_fields:
                    fieldnames.append(field)
                    all_fields.remove(field)

            # 然后添加剩余的字段（按字母顺序）
            fieldnames.extend(sorted(all_fields))

            # 如果没有账号，使用默认字段
            if not fieldnames:
                fieldnames = preferred_order

            # 记录最终确定的字段列表
            logger.info(f"CSV表头字段顺序: {fieldnames}")

            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()

            for account in accounts:
                # 确保所有字段都是字符串类型，避免CSV写入错误
                row = {}
                # 先为所有字段设置默认值为空字符串
                for field in fieldnames:
                    row[field] = ""

                # 然后填充账号中存在的字段
                for key, value in account.items():
                    if value is None:
                        row[key] = ""
                    elif isinstance(value, (dict, list)):
                        # 对于复杂类型，转换为JSON字符串
                        try:
                            row[key] = json.dumps(value, ensure_ascii=False)
                        except Exception as e:
                            logger.warning(f"无法序列化字段 {key}: {str(e)}")
                            row[key] = str(value)
                    elif key == "recovery_code":
                        # 特殊处理recovery_code字段，确保它是字符串
                        row[key] = str(value).strip()
                        logger.info(f"导出账号 {account.get('username', '未知')} 的恢复码: {row[key]}")
                    else:
                        row[key] = str(value)

                try:
                    writer.writerow(row)
                except Exception as row_error:
                    # 记录写入行时的错误，但继续处理其他行
                    logger.error(f"写入CSV行失败: {str(row_error)}, 账号ID: {row.get('id', '未知')}")
                    # 尝试找出问题字段
                    for field in fieldnames:
                        if field not in row:
                            logger.error(f"缺少字段: {field}")
                        elif not isinstance(row[field], str):
                            logger.error(f"字段 {field} 不是字符串类型: {type(row[field])}")

            # 设置响应头，指定为CSV文件
            headers = {
                'Content-Disposition': f'attachment; filename="accounts_export_{datetime.now().strftime("%Y%m%d")}.csv"'
            }

            return Response(
                content=output.getvalue(),
                media_type="text/csv",
                headers=headers
            )
        else:
            # 默认返回JSON格式
            export_data = {
                "accounts": accounts,
                "export_time": datetime.now().isoformat()
            }

            return export_data
    except Exception as e:
        logger.error(f"导出账号失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"导出账号失败: {str(e)}"
        )

# API端点：打开账号关联的设备
@router.post("/accounts/{account_id}/open-device")
async def open_account_device(
    account_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    打开账号关联的设备并启动V2rayN

    - **account_id**: 账号ID
    """
    try:
        logger.info(f"收到打开设备请求，账号ID: {account_id}")

        # 获取账号信息
        account = await db_service.get_account(account_id)
        if not account:
            raise HTTPException(status_code=404, detail="账号不存在")

        # 通过device_account_mappings集合查找账号关联的设备
        try:
            logger.info(f"开始查找账号 {account_id} 的设备关联")

            # 收集所有可能的账号ID格式进行查询
            possible_account_ids = [account_id]

            # 如果是ObjectId格式，也添加ObjectId对象
            from bson import ObjectId
            if ObjectId.is_valid(account_id):
                possible_account_ids.append(ObjectId(account_id))

            # 如果账号有_id字段，也尝试使用_id
            if account and '_id' in account:
                account_mongo_id = str(account['_id'])
                if account_mongo_id not in possible_account_ids:
                    possible_account_ids.append(account_mongo_id)
                if ObjectId.is_valid(account_mongo_id):
                    possible_account_ids.append(ObjectId(account_mongo_id))

            logger.info(f"尝试使用以下ID查找设备映射: {possible_account_ids}")

            # 尝试所有可能的ID格式
            mapping = None
            for aid in possible_account_ids:
                logger.info(f"尝试查找映射，account_id: {aid} (类型: {type(aid)})")
                mapping = await db_service.db.device_account_mappings.find_one({
                    "account_id": aid,
                    "status": "active"
                })
                if mapping:
                    logger.info(f"找到设备映射: {mapping}")
                    break

            if not mapping:
                # 尝试查找所有状态的映射（不限制status）
                logger.info("未找到active状态的映射，尝试查找所有状态的映射")
                for aid in possible_account_ids:
                    mapping = await db_service.db.device_account_mappings.find_one({
                        "account_id": aid
                    })
                    if mapping:
                        logger.info(f"找到设备映射（任意状态）: {mapping}")
                        break

            if not mapping:
                logger.warning(f"账号 {account_id} 未找到任何设备映射")
                # 列出所有映射以便调试
                all_mappings = await db_service.db.device_account_mappings.find({}).to_list(length=10)
                logger.info(f"数据库中现有的设备映射（前10条）: {all_mappings}")
                raise HTTPException(status_code=400, detail="账号未关联设备")

            device_id = mapping.get('device_id')
            if not device_id:
                logger.warning(f"账号 {account_id} 的设备映射中缺少device_id字段: {mapping}")
                raise HTTPException(status_code=400, detail="账号关联的设备信息不完整")

            logger.info(f"账号 {account_id} 关联的设备ID: {device_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"查找账号关联设备失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"查找账号关联设备失败: {str(e)}")

        # 调用Core服务启动设备
        from app.services.device_service_factory import DeviceServiceFactory

        device_client = await DeviceServiceFactory.create_client()
        if not device_client:
            raise HTTPException(status_code=503, detail="Core服务不可用，请检查Core服务是否正常运行")

        try:
            # 启动设备
            logger.info(f"启动设备: {device_id}")
            start_success = await device_client.start_device(str(device_id))

            if start_success:
                # 执行网络连接和V2rayN启动命令
                logger.info(f"设备启动成功，执行网络连接检查和V2rayN启动")

                try:
                    # 调用设备命令来启动V2rayN
                    command_result = await device_client.execute_command(
                        str(device_id),
                        "launch_with_network_check",
                        {}
                    )

                    if command_result.get('success', False):
                        logger.info(f"设备 {device_id} 启动成功，V2rayN已连接")
                        return {
                            "success": True,
                            "message": "设备启动成功，V2rayN已连接"
                        }
                    else:
                        logger.warning(f"设备启动成功但V2rayN连接失败: {command_result.get('error', '未知错误')}")
                        return {
                            "success": True,
                            "message": f"设备启动成功，但V2rayN连接失败: {command_result.get('error', '未知错误')}"
                        }
                except Exception as cmd_error:
                    logger.warning(f"设备启动成功但执行网络检查命令失败: {str(cmd_error)}")
                    return {
                        "success": True,
                        "message": f"设备启动成功，但网络检查失败: {str(cmd_error)}"
                    }
            else:
                logger.error(f"设备 {device_id} 启动失败")
                return {
                    "success": False,
                    "message": "设备启动失败"
                }
        except Exception as start_error:
            error_msg = str(start_error)
            if "Connection refused" in error_msg or "UNAVAILABLE" in error_msg:
                logger.error(f"Core服务连接失败: {error_msg}")
                raise HTTPException(
                    status_code=503,
                    detail="Core服务不可用，无法启动设备。请检查Core服务是否正常运行。"
                )
            else:
                logger.error(f"启动设备时发生未知错误: {error_msg}")
                raise HTTPException(
                    status_code=500,
                    detail=f"启动设备失败: {error_msg}"
                )

        finally:
            # 关闭客户端连接
            if device_client and hasattr(device_client, 'close'):
                try:
                    await device_client.close()
                except Exception as close_error:
                    logger.warning(f"关闭设备客户端连接失败: {str(close_error)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"打开设备失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"打开设备失败: {str(e)}"
        )