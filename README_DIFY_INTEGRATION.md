# ThunderHub Dify工作流集成

## 🎯 项目概述

ThunderHub现已完全集成Dify工作流调用能力，实现了通用的AI功能扩展框架。用户可以通过配置Dify实例和工作流，在视频处理流程中无缝调用各种AI服务，如内容生成、智能分析、自动化处理等。

## ✨ 核心特性

### 🔧 完整的管理系统
- **实例管理**: 支持多个Dify服务实例配置和管理
- **工作流配置**: 灵活的参数映射和工作流配置
- **执行监控**: 实时监控执行状态、日志和结果
- **模板管理**: 预置工作流模板，快速启用AI功能

### 🚀 强大的集成能力
- **工作流引擎集成**: 新增`dify_workflow`步骤类型
- **上下文变量支持**: 支持`${variable}`语法的动态参数
- **参数映射**: 灵活的输入输出参数映射机制
- **错误处理**: 完善的重试机制和错误处理

### 🎨 用户友好界面
- **Vue3管理界面**: 现代化的管理界面
- **实时状态监控**: 实例状态、执行进度实时更新
- **模板浏览**: 可视化的工作流模板管理
- **详细日志**: 完整的执行日志和错误信息

## 📁 项目结构

```
ThunderHub/
├── backend/app/
│   ├── core/schemas/dify_models.py          # Dify数据模型
│   ├── services/
│   │   ├── dify_client.py                   # Dify HTTP客户端
│   │   └── dify_service.py                  # Dify业务服务
│   └── api/v1/dify.py                       # Dify REST API
├── frontend/src/
│   ├── api/dify.ts                          # 前端API客户端
│   └── views/system/
│       ├── DifyManagement.vue               # 主管理页面
│       └── components/
│           ├── InstanceDialog.vue           # 实例配置对话框
│           ├── WorkflowDialog.vue           # 工作流配置对话框
│           └── ExecutionDetailDialog.vue    # 执行详情对话框
├── core/
│   ├── src/services/common/workflow_engine.py  # 工作流引擎(已扩展)
│   └── config/
│       ├── workflows/
│       │   └── dify_content_generation.yaml    # Dify内容生成模板
│       └── platforms/youtube/workflows/
│           └── ai_enhanced_upload.yaml         # AI增强上传模板
├── docs/dify_integration_guide.md           # 详细使用指南
├── tests/test_dify_integration.py           # 集成测试
└── README_DIFY_INTEGRATION.md               # 本文件
```

## 🚀 快速开始

### 1. 启动服务

确保后端和前端服务正常运行：

```bash
# 启动后端
cd backend
uvicorn app.main:app --reload

# 启动前端
cd frontend  
npm run dev
```

### 2. 配置Dify实例

访问 `http://localhost:3000/system/dify-management`，在"实例管理"标签页中：

1. 点击"添加实例"
2. 填写Dify服务信息：
   - 实例名称：如"内容生成器"
   - API地址：如`https://api.dify.ai/v1`
   - API密钥：你的Dify API Key
   - 描述和其他配置
3. 点击"测试连接"验证配置
4. 保存实例

### 3. 配置工作流

在"工作流配置"标签页中：

1. 点击"添加工作流"
2. 选择关联的Dify实例
3. 配置工作流参数：
   - 工作流名称
   - Dify工作流ID
   - 输入输出参数映射
4. 保存配置

### 4. 使用预置模板

在"工作流模板"标签页中：

1. 浏览可用的工作流模板
2. 查看模板详情和配置
3. 根据模板创建自己的工作流

## 🎯 使用场景

### 场景1: YouTube视频AI内容生成

使用`ai_enhanced_upload.yaml`模板，自动生成视频标题、描述和标签：

```yaml
# 在工作流中使用
- name: "AI生成视频标题"
  action: "dify_workflow"
  parameters:
    instance_id: "content_generator"
    workflow_id: "youtube_title_generator"
    inputs:
      video_topic: "${video_topic}"
      target_keywords: "${keywords}"
    output_mappings:
      title: "ai_generated_title"
```

### 场景2: 内容质量检查

```yaml
- name: "内容合规检查"
  action: "dify_workflow"
  parameters:
    instance_id: "content_checker"
    workflow_id: "compliance_checker"
    inputs:
      content: "${video_description}"
      platform: "youtube"
    output_mappings:
      score: "compliance_score"
      suggestions: "improvement_tips"
```

### 场景3: 多语言内容生成

```yaml
- name: "生成多语言标题"
  action: "dify_workflow"
  parameters:
    instance_id: "translator"
    workflow_id: "multi_language_generator"
    inputs:
      original_title: "${title}"
      target_languages: ["en", "ja", "ko"]
    output_mappings:
      translations: "multilingual_titles"
```

## 🔧 技术架构

### 后端架构
- **FastAPI**: REST API框架
- **Pydantic**: 数据验证和序列化
- **Motor**: 异步MongoDB驱动
- **aiohttp**: 异步HTTP客户端
- **Tenacity**: 重试机制

### 前端架构
- **Vue3**: 响应式前端框架
- **TypeScript**: 类型安全
- **Element Plus**: UI组件库
- **Composition API**: 组合式API

### 集成架构
- **工作流引擎**: 扩展支持Dify步骤
- **参数映射**: 灵活的数据转换
- **上下文管理**: 步骤间数据传递
- **错误处理**: 完善的异常处理机制

## 📊 功能特性

| 功能模块 | 状态 | 描述 |
|---------|------|------|
| 实例管理 | ✅ 完成 | CRUD操作、连接测试、状态管理 |
| 工作流配置 | ✅ 完成 | 参数映射、类型配置、启用禁用 |
| 执行监控 | ✅ 完成 | 实时状态、执行日志、错误追踪 |
| 模板管理 | ✅ 完成 | 模板浏览、详情查看、分类筛选 |
| 工作流集成 | ✅ 完成 | 新步骤类型、上下文变量、参数映射 |
| 前端界面 | ✅ 完成 | 管理界面、对话框、状态显示 |
| API接口 | ✅ 完成 | RESTful API、错误处理、文档 |
| 测试用例 | ✅ 完成 | 单元测试、集成测试、场景测试 |
| 使用文档 | ✅ 完成 | 详细指南、API文档、最佳实践 |

## 🧪 测试

运行测试套件：

```bash
# 运行所有Dify集成测试
pytest tests/test_dify_integration.py -v

# 运行特定测试类
pytest tests/test_dify_integration.py::TestDifyClient -v

# 运行覆盖率测试
pytest tests/test_dify_integration.py --cov=backend.app.services.dify
```

## 📚 文档

- **详细使用指南**: [docs/dify_integration_guide.md](docs/dify_integration_guide.md)
- **API文档**: 访问 `http://localhost:8000/docs`
- **工作流模板**: `core/config/workflows/`

## 🔮 未来规划

### 短期计划
- [ ] 添加更多预置工作流模板
- [ ] 支持流式响应模式
- [ ] 增强错误处理和日志记录
- [ ] 添加性能监控和统计

### 长期计划
- [ ] 支持自定义Dify应用类型
- [ ] 集成更多AI服务提供商
- [ ] 可视化工作流编辑器
- [ ] 智能推荐和优化建议

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢Dify团队提供优秀的AI工作流平台，使得这个集成成为可能。

---

**ThunderHub Team** - 让视频处理更智能 🚀
