#!/usr/bin/env python3
"""
测试创建社交账号功能
"""

import asyncio
import sys
import os
import json
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.api.v1.social_accounts import CreateSocialAccount
from app.core.schemas.social_repository import SocialDatabaseService
from app.config.settings import settings

async def test_create_account():
    """测试创建账号功能"""
    try:
        print("开始测试创建社交账号...")
        
        # 创建数据库服务
        db_service = SocialDatabaseService()
        
        # 创建测试账号数据
        test_account = CreateSocialAccount(
            username="test_user",
            password="test123",
            platform_id="681efeeecd836bd64b9c2a1e",
            core_service_id="core-123.2-3",
            status="active"
        )
        
        print(f"测试账号数据: {test_account.model_dump()}")
        
        # 获取账号数据
        account_data = test_account.model_dump()
        
        # 生成唯一的账号ID
        unique_id = f"{account_data['platform_id']}_{account_data['username']}_{int(time.time())}"
        account_data["id"] = unique_id
        print(f"生成唯一账号ID: {unique_id}")
        
        # 添加创建和更新时间
        from datetime import datetime
        now = datetime.now()
        account_data["created_at"] = now
        account_data["updated_at"] = now
        
        print(f"最终账号数据: {json.dumps(account_data, default=str, indent=2)}")
        
        # 创建账号
        account_id = await db_service.create_account(account_data)
        print(f"账号创建成功，ID: {account_id}")
        
        # 获取创建的账号
        created_account = await db_service.get_account(account_id)
        print(f"获取到的账号: {json.dumps(created_account, default=str, indent=2)}")
        
        print("测试成功完成！")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_create_account())
