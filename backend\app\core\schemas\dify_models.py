"""
Dify工作流集成相关的数据模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class DifyInstanceStatus(str, Enum):
    """Dify实例状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"


class DifyWorkflowType(str, Enum):
    """Dify工作流类型"""
    CHATBOT = "chatbot"
    WORKFLOW = "workflow"
    AGENT = "agent"
    COMPLETION = "completion"


class DifyExecutionStatus(str, Enum):
    """Dify工作流执行状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DifyParameterMapping(BaseModel):
    """Dify参数映射配置"""
    source_key: str = Field(..., description="源参数键名")
    target_key: str = Field(..., description="目标参数键名")
    data_type: str = Field(default="string", description="数据类型")
    default_value: Optional[Any] = Field(None, description="默认值")
    required: bool = Field(default=True, description="是否必需")
    description: Optional[str] = Field(None, description="参数描述")


class DifyWorkflowConfig(BaseModel):
    """Dify工作流配置"""
    id: Optional[str] = Field(None, description="配置ID")
    name: str = Field(..., description="工作流名称")
    workflow_id: str = Field(..., description="Dify工作流ID")
    workflow_type: DifyWorkflowType = Field(..., description="工作流类型")
    description: Optional[str] = Field(None, description="工作流描述")
    
    # 参数映射
    input_mappings: List[DifyParameterMapping] = Field(default_factory=list, description="输入参数映射")
    output_mappings: List[DifyParameterMapping] = Field(default_factory=list, description="输出参数映射")
    
    # 执行配置
    timeout: int = Field(default=300, description="超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: int = Field(default=5, description="重试延迟(秒)")
    
    # 元数据
    tags: List[str] = Field(default_factory=list, description="标签")
    enabled: bool = Field(default=True, description="是否启用")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class DifyInstanceConfig(BaseModel):
    """Dify实例配置"""
    id: Optional[str] = Field(None, description="实例ID")
    name: str = Field(..., description="实例名称")
    base_url: str = Field(..., description="Dify API基础URL")
    api_key: str = Field(..., description="API密钥")
    
    # 连接配置
    timeout: int = Field(default=30, description="连接超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: int = Field(default=2, description="重试延迟(秒)")
    
    # 状态信息
    status: DifyInstanceStatus = Field(default=DifyInstanceStatus.INACTIVE, description="实例状态")
    last_test_at: Optional[datetime] = Field(None, description="最后测试时间")
    last_error: Optional[str] = Field(None, description="最后错误信息")
    
    # 工作流配置
    workflows: List[DifyWorkflowConfig] = Field(default_factory=list, description="工作流配置列表")
    
    # 元数据
    description: Optional[str] = Field(None, description="实例描述")
    tags: List[str] = Field(default_factory=list, description="标签")
    enabled: bool = Field(default=True, description="是否启用")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    @validator('base_url')
    def validate_base_url(cls, v):
        """验证基础URL格式"""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('base_url必须以http://或https://开头')
        return v.rstrip('/')


class DifyExecutionRequest(BaseModel):
    """Dify工作流执行请求"""
    instance_id: str = Field(..., description="Dify实例ID")
    workflow_id: str = Field(..., description="工作流ID")
    inputs: Dict[str, Any] = Field(default_factory=dict, description="输入参数")
    
    # 执行选项
    user_id: Optional[str] = Field(None, description="用户ID")
    conversation_id: Optional[str] = Field(None, description="对话ID")
    response_mode: str = Field(default="blocking", description="响应模式: blocking/streaming")
    
    # 元数据
    task_id: Optional[str] = Field(None, description="关联任务ID")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class DifyExecutionResponse(BaseModel):
    """Dify工作流执行响应"""
    success: bool = Field(..., description="是否成功")
    execution_id: Optional[str] = Field(None, description="执行ID")
    status: DifyExecutionStatus = Field(..., description="执行状态")
    
    # 结果数据
    outputs: Dict[str, Any] = Field(default_factory=dict, description="输出结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    # 执行信息
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration: Optional[float] = Field(None, description="执行时长(秒)")
    
    # 使用统计
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token使用统计")
    cost: Optional[float] = Field(None, description="执行成本")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class DifyExecutionLog(BaseModel):
    """Dify工作流执行日志"""
    id: Optional[str] = Field(None, description="日志ID")
    execution_id: str = Field(..., description="执行ID")
    task_id: Optional[str] = Field(None, description="关联任务ID")
    
    # 执行信息
    instance_id: str = Field(..., description="Dify实例ID")
    workflow_id: str = Field(..., description="工作流ID")
    status: DifyExecutionStatus = Field(..., description="执行状态")
    
    # 请求和响应
    request_data: Dict[str, Any] = Field(default_factory=dict, description="请求数据")
    response_data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")
    
    # 时间信息
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration: Optional[float] = Field(None, description="执行时长(秒)")
    
    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    retry_count: int = Field(default=0, description="重试次数")
    
    # 使用统计
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token使用统计")
    cost: Optional[float] = Field(None, description="执行成本")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")
    created_at: Optional[datetime] = Field(None, description="创建时间")


class DifyTestRequest(BaseModel):
    """Dify连接测试请求"""
    instance_id: Optional[str] = Field(None, description="实例ID(用于测试已保存的实例)")
    base_url: Optional[str] = Field(None, description="基础URL(用于测试新配置)")
    api_key: Optional[str] = Field(None, description="API密钥(用于测试新配置)")
    timeout: int = Field(default=10, description="测试超时时间(秒)")


class DifyTestResponse(BaseModel):
    """Dify连接测试响应"""
    success: bool = Field(..., description="测试是否成功")
    message: str = Field(..., description="测试结果消息")
    response_time: Optional[float] = Field(None, description="响应时间(秒)")
    api_version: Optional[str] = Field(None, description="API版本")
    error_details: Optional[str] = Field(None, description="错误详情")
    tested_at: datetime = Field(default_factory=datetime.now, description="测试时间")


# 用于API响应的包装模型
class DifyInstanceListResponse(BaseModel):
    """Dify实例列表响应"""
    success: bool = Field(True, description="是否成功")
    data: List[DifyInstanceConfig] = Field(..., description="实例列表")
    total: int = Field(..., description="总数量")
    message: str = Field(default="获取成功", description="响应消息")


class DifyWorkflowListResponse(BaseModel):
    """Dify工作流列表响应"""
    success: bool = Field(True, description="是否成功")
    data: List[DifyWorkflowConfig] = Field(..., description="工作流列表")
    total: int = Field(..., description="总数量")
    message: str = Field(default="获取成功", description="响应消息")


class DifyExecutionListResponse(BaseModel):
    """Dify执行记录列表响应"""
    success: bool = Field(True, description="是否成功")
    data: List[DifyExecutionLog] = Field(..., description="执行记录列表")
    total: int = Field(..., description="总数量")
    message: str = Field(default="获取成功", description="响应消息")
