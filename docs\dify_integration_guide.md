# Dify工作流集成指南

## 概述

ThunderHub已集成Dify工作流调用能力，允许在视频处理工作流中无缝调用Dify AI服务，实现智能内容生成、处理和分析功能。

## 功能特性

### 🚀 核心功能
- **多实例管理**: 支持配置多个Dify服务实例
- **工作流配置**: 灵活的工作流参数映射和配置
- **执行监控**: 实时监控Dify工作流执行状态和结果
- **模板管理**: 预置工作流模板，快速启用AI功能
- **错误处理**: 完善的错误处理和重试机制

### 🎯 应用场景
- **内容生成**: 自动生成视频标题、描述、标签
- **内容分析**: 视频内容质量检查和合规性分析
- **智能推荐**: 基于内容的标签和分类推荐
- **多语言处理**: 内容翻译和本地化
- **创意辅助**: 缩略图文案、营销文案生成

## 快速开始

### 1. 配置Dify实例

在系统管理 → Dify工作流管理 → 实例管理中添加Dify实例：

```json
{
  "name": "内容生成器",
  "base_url": "https://api.dify.ai/v1",
  "api_key": "your-dify-api-key",
  "description": "用于内容生成的Dify实例",
  "timeout": 60,
  "max_retries": 3,
  "enabled": true
}
```

### 2. 配置工作流

在工作流配置标签页中添加Dify工作流配置：

```json
{
  "name": "YouTube标题生成",
  "workflow_id": "title_generator",
  "workflow_type": "workflow",
  "input_mappings": [
    {
      "source_key": "video_topic",
      "target_key": "topic",
      "data_type": "string",
      "required": true
    }
  ],
  "output_mappings": [
    {
      "source_key": "title",
      "target_key": "generated_title",
      "data_type": "string"
    }
  ]
}
```

### 3. 在工作流中使用

在YAML工作流配置中添加Dify步骤：

```yaml
- name: "AI生成视频标题"
  id: "ai_generate_title"
  action: "dify_workflow"
  required: true
  timeout: 60
  parameters:
    instance_id: "content_generator"
    workflow_id: "title_generator"
    inputs:
      video_topic: "${video_topic}"
      target_keywords: "${target_keywords}"
    output_mappings:
      title: "ai_generated_title"
```

## 详细配置

### Dify实例配置

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| name | string | ✓ | 实例名称 |
| base_url | string | ✓ | Dify API基础URL |
| api_key | string | ✓ | API密钥 |
| description | string | - | 实例描述 |
| timeout | integer | - | 请求超时时间(秒)，默认60 |
| max_retries | integer | - | 最大重试次数，默认3 |
| retry_delay | float | - | 重试延迟(秒)，默认1.0 |
| enabled | boolean | - | 是否启用，默认true |
| tags | array | - | 标签列表 |

### 工作流配置

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| name | string | ✓ | 工作流名称 |
| workflow_id | string | ✓ | Dify工作流ID |
| workflow_type | string | ✓ | 工作流类型(workflow/chatbot/agent/completion) |
| description | string | - | 工作流描述 |
| input_mappings | array | - | 输入参数映射 |
| output_mappings | array | - | 输出参数映射 |
| timeout | integer | - | 执行超时时间(秒) |
| max_retries | integer | - | 最大重试次数 |
| retry_delay | float | - | 重试延迟(秒) |
| enabled | boolean | - | 是否启用 |

### 参数映射配置

```json
{
  "source_key": "源参数名",
  "target_key": "目标参数名", 
  "data_type": "数据类型(string/number/boolean/object/array)",
  "required": true,
  "default_value": "默认值",
  "description": "参数描述"
}
```

## 工作流步骤配置

### 基本语法

```yaml
- name: "步骤名称"
  id: "步骤ID"
  action: "dify_workflow"
  required: true
  timeout: 60
  parameters:
    instance_id: "实例ID"
    workflow_id: "工作流ID"
    inputs:
      param1: "${context_variable}"
      param2: "固定值"
    output_mappings:
      output1: "context_variable_name"
```

### 上下文变量

在工作流中可以使用 `${variable_name}` 语法引用上下文变量：

- `${video_topic}` - 视频主题
- `${video_duration}` - 视频时长
- `${generated_title}` - 前面步骤生成的标题
- 自定义变量

### 输出映射

Dify工作流的输出会根据 `output_mappings` 配置保存到工作流上下文中：

```yaml
output_mappings:
  title: "ai_generated_title"        # Dify输出的title保存为ai_generated_title
  description: "ai_generated_desc"   # Dify输出的description保存为ai_generated_desc
```

## 预置模板

系统提供了多个预置的工作流模板：

### 1. AI内容生成工作流
- **文件**: `core/config/workflows/dify_content_generation.yaml`
- **功能**: 生成视频标题、描述、标签
- **适用**: 内容创作、SEO优化

### 2. AI增强YouTube上传
- **文件**: `core/config/platforms/youtube/workflows/ai_enhanced_upload.yaml`
- **功能**: 集成AI内容生成的YouTube上传流程
- **适用**: 自动化视频发布

## API接口

### 实例管理
- `GET /api/v1/dify/instances` - 获取实例列表
- `POST /api/v1/dify/instances` - 创建实例
- `PUT /api/v1/dify/instances/{id}` - 更新实例
- `DELETE /api/v1/dify/instances/{id}` - 删除实例
- `POST /api/v1/dify/instances/test` - 测试实例连接

### 工作流管理
- `GET /api/v1/dify/workflows` - 获取工作流列表
- `POST /api/v1/dify/workflows` - 创建工作流配置
- `PUT /api/v1/dify/workflows/{id}` - 更新工作流配置
- `DELETE /api/v1/dify/workflows/{id}` - 删除工作流配置

### 执行管理
- `POST /api/v1/dify/execute` - 执行工作流
- `GET /api/v1/dify/executions` - 获取执行记录
- `GET /api/v1/dify/executions/{id}` - 获取执行详情

### 模板管理
- `GET /api/v1/dify/workflow-templates` - 获取工作流模板列表
- `GET /api/v1/dify/workflow-templates/{id}` - 获取模板详情

## 错误处理

### 常见错误

1. **连接失败**
   - 检查Dify实例URL和API密钥
   - 确认网络连接正常

2. **工作流执行失败**
   - 检查输入参数是否正确
   - 确认Dify工作流ID存在且可用

3. **超时错误**
   - 增加timeout配置
   - 检查Dify服务响应时间

### 重试机制

系统提供自动重试机制：
- 默认重试3次
- 重试间隔递增
- 可配置重试策略

## 最佳实践

### 1. 实例配置
- 为不同用途创建不同的实例
- 设置合理的超时时间
- 使用描述性的名称和标签

### 2. 工作流设计
- 合理设置步骤依赖关系
- 使用有意义的参数映射
- 添加错误处理步骤

### 3. 性能优化
- 避免在循环中调用Dify
- 使用缓存减少重复调用
- 监控执行时间和成功率

### 4. 安全考虑
- 妥善保管API密钥
- 定期轮换密钥
- 限制API访问权限

## 故障排除

### 调试步骤
1. 检查实例配置和连接状态
2. 查看执行记录和错误日志
3. 验证输入参数格式
4. 测试Dify工作流独立运行

### 日志查看
- 后端日志: 查看API调用和错误信息
- 工作流日志: 查看步骤执行状态
- Dify日志: 查看工作流执行详情

## 扩展开发

### 添加新的步骤类型
1. 在 `workflow_engine.py` 中添加新的action处理
2. 实现对应的执行方法
3. 更新工作流配置模板

### 自定义参数处理
1. 扩展参数映射逻辑
2. 添加数据类型转换
3. 实现自定义验证规则

## 更新日志

### v1.0.0 (2024-08-01)
- 初始版本发布
- 支持基本的Dify工作流集成
- 提供管理界面和API接口
- 包含预置工作流模板
