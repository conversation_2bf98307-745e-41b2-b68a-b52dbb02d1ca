"""
Dify集成功能测试
"""

import pytest
import asyncio
import json
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from fastapi.testclient import TestClient

from backend.app.main import app
from backend.app.services.dify_service import DifyService
from backend.app.services.dify_client import DifyClient
from backend.app.core.schemas.dify_models import (
    DifyInstanceConfig, 
    DifyWorkflowConfig, 
    DifyExecutionRequest
)


class TestDifyClient:
    """测试Dify客户端"""
    
    @pytest.fixture
    def dify_client(self):
        """创建Dify客户端实例"""
        config = DifyInstanceConfig(
            name="测试实例",
            base_url="https://api.dify.ai/v1",
            api_key="test-api-key",
            description="测试用Dify实例"
        )
        return DifyClient(config)
    
    @pytest.mark.asyncio
    async def test_connection_test(self, dify_client):
        """测试连接测试功能"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟成功响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {"status": "ok"}
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await dify_client.test_connection()
            assert result is True
    
    @pytest.mark.asyncio
    async def test_workflow_execution(self, dify_client):
        """测试工作流执行"""
        with patch('aiohttp.ClientSession.post') as mock_post:
            # 模拟成功响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {
                "workflow_run_id": "test-run-id",
                "task_id": "test-task-id",
                "data": {
                    "outputs": {
                        "title": "生成的标题",
                        "description": "生成的描述"
                    }
                }
            }
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await dify_client.execute_workflow(
                workflow_id="test-workflow",
                inputs={"topic": "测试主题"},
                response_mode="blocking"
            )
            
            assert result["workflow_run_id"] == "test-run-id"
            assert result["data"]["outputs"]["title"] == "生成的标题"


class TestDifyService:
    """测试Dify服务"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库"""
        return Mock()
    
    @pytest.fixture
    def dify_service(self, mock_db):
        """创建Dify服务实例"""
        return DifyService(mock_db)
    
    @pytest.mark.asyncio
    async def test_create_instance(self, dify_service, mock_db):
        """测试创建实例"""
        instance_data = DifyInstanceConfig(
            name="测试实例",
            base_url="https://api.dify.ai/v1",
            api_key="test-api-key"
        )
        
        # 模拟数据库插入
        mock_db.instances_collection.insert_one.return_value = Mock(
            inserted_id="test-instance-id"
        )
        
        result = await dify_service.create_instance(instance_data)
        assert result["id"] == "test-instance-id"
        assert result["name"] == "测试实例"
    
    @pytest.mark.asyncio
    async def test_execute_workflow(self, dify_service):
        """测试执行工作流"""
        with patch.object(dify_service, 'get_instance') as mock_get_instance, \
             patch.object(dify_service, 'get_workflow') as mock_get_workflow, \
             patch('backend.app.services.dify_client.DifyClient') as mock_client_class:
            
            # 模拟实例和工作流配置
            mock_get_instance.return_value = {
                "id": "test-instance",
                "name": "测试实例",
                "base_url": "https://api.dify.ai/v1",
                "api_key": "test-key"
            }
            
            mock_get_workflow.return_value = {
                "id": "test-workflow",
                "workflow_id": "dify-workflow-id",
                "input_mappings": [],
                "output_mappings": []
            }
            
            # 模拟客户端执行
            mock_client = AsyncMock()
            mock_client.execute_workflow.return_value = {
                "workflow_run_id": "test-run",
                "data": {"outputs": {"result": "success"}}
            }
            mock_client_class.return_value = mock_client
            
            request = DifyExecutionRequest(
                instance_id="test-instance",
                workflow_id="test-workflow",
                inputs={"input": "test"},
                response_mode="blocking"
            )
            
            result = await dify_service.execute_workflow(request)
            assert result["success"] is True
            assert result["outputs"]["result"] == "success"


class TestDifyAPI:
    """测试Dify API接口"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_get_instances(self, client):
        """测试获取实例列表"""
        with patch('backend.app.api.v1.dify.get_dify_service') as mock_service:
            mock_service.return_value.list_instances.return_value = []
            
            response = client.get("/api/v1/dify/instances")
            assert response.status_code == 200
            assert response.json()["success"] is True
    
    def test_create_instance(self, client):
        """测试创建实例"""
        instance_data = {
            "name": "测试实例",
            "base_url": "https://api.dify.ai/v1",
            "api_key": "test-key",
            "description": "测试描述"
        }
        
        with patch('backend.app.api.v1.dify.get_dify_service') as mock_service:
            mock_service.return_value.create_instance.return_value = {
                "id": "test-id",
                **instance_data
            }
            
            response = client.post("/api/v1/dify/instances", json=instance_data)
            assert response.status_code == 200
            assert response.json()["success"] is True
    
    def test_execute_workflow(self, client):
        """测试执行工作流"""
        execution_data = {
            "instance_id": "test-instance",
            "workflow_id": "test-workflow",
            "inputs": {"topic": "测试主题"},
            "response_mode": "blocking"
        }
        
        with patch('backend.app.api.v1.dify.get_dify_service') as mock_service:
            mock_service.return_value.execute_workflow.return_value = {
                "success": True,
                "execution_id": "test-execution",
                "outputs": {"title": "生成的标题"}
            }
            
            response = client.post("/api/v1/dify/execute", json=execution_data)
            assert response.status_code == 200
            assert response.json()["success"] is True
    
    def test_get_workflow_templates(self, client):
        """测试获取工作流模板"""
        with patch('os.path.exists') as mock_exists, \
             patch('pathlib.Path.glob') as mock_glob, \
             patch('builtins.open') as mock_open:
            
            mock_exists.return_value = True
            mock_glob.return_value = [Mock(stem="test_template")]
            mock_open.return_value.__enter__.return_value.read.return_value = """
workflow:
  name: "测试模板"
  description: "测试工作流模板"
  steps:
    - action: "dify_workflow"
metadata:
  tags: ["测试"]
"""
            
            response = client.get("/api/v1/dify/workflow-templates")
            assert response.status_code == 200
            assert response.json()["success"] is True


class TestWorkflowEngine:
    """测试工作流引擎Dify集成"""
    
    @pytest.mark.asyncio
    async def test_dify_workflow_step(self):
        """测试Dify工作流步骤执行"""
        from core.src.services.common.workflow_engine import WorkflowEngine
        
        # 创建工作流引擎实例
        engine = WorkflowEngine("test_config.yaml", task_id="test-task")
        
        # 模拟步骤配置
        step = {
            "id": "test_step",
            "name": "测试Dify步骤",
            "action": "dify_workflow",
            "parameters": {
                "instance_id": "test-instance",
                "workflow_id": "test-workflow",
                "inputs": {
                    "topic": "${video_topic}"
                },
                "output_mappings": {
                    "title": "generated_title"
                }
            }
        }
        
        # 设置工作流上下文
        engine.workflow_context = {"video_topic": "测试视频主题"}
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            # 模拟API响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {
                "success": True,
                "outputs": {"title": "AI生成的标题"}
            }
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await engine._execute_dify_workflow_action(step)
            assert result is True
            assert engine.workflow_context["generated_title"] == "AI生成的标题"


class TestIntegrationScenarios:
    """集成场景测试"""
    
    @pytest.mark.asyncio
    async def test_content_generation_workflow(self):
        """测试内容生成工作流场景"""
        # 模拟完整的内容生成流程
        workflow_context = {
            "video_topic": "Python编程教程",
            "video_duration": 600,
            "target_keywords": ["Python", "编程", "教程"]
        }
        
        # 步骤1: 生成标题
        title_step = {
            "action": "dify_workflow",
            "parameters": {
                "instance_id": "content_generator",
                "workflow_id": "title_generator",
                "inputs": {
                    "topic": "${video_topic}",
                    "keywords": "${target_keywords}"
                },
                "output_mappings": {
                    "title": "generated_title"
                }
            }
        }
        
        # 步骤2: 生成描述
        description_step = {
            "action": "dify_workflow", 
            "parameters": {
                "instance_id": "content_generator",
                "workflow_id": "description_generator",
                "inputs": {
                    "title": "${generated_title}",
                    "topic": "${video_topic}"
                },
                "output_mappings": {
                    "description": "generated_description"
                }
            }
        }
        
        # 模拟执行结果
        expected_results = {
            "generated_title": "Python编程入门：从零开始学习编程",
            "generated_description": "本视频将带你从零开始学习Python编程..."
        }
        
        # 验证工作流能够正确处理步骤依赖和上下文传递
        assert "video_topic" in workflow_context
        assert title_step["parameters"]["inputs"]["topic"] == "${video_topic}"
        assert description_step["parameters"]["inputs"]["title"] == "${generated_title}"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
