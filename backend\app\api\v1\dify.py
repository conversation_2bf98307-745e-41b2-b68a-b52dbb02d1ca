"""
Dify工作流管理API
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.config.database import get_database
from app.core.schemas.dify_models import (
    DifyInstanceConfig,
    DifyWorkflowConfig,
    DifyExecutionRequest,
    DifyExecutionResponse,
    DifyExecutionLog,
    DifyTestRequest,
    DifyTestResponse,
    DifyInstanceListResponse,
    DifyWorkflowListResponse,
    DifyExecutionListResponse
)
from app.services.dify_service import DifyService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/dify", tags=["Dify工作流"])


def get_dify_service(db: AsyncIOMotorDatabase = Depends(get_database)) -> DifyService:
    """获取Dify服务实例"""
    return DifyService(db)


# Dify实例管理
@router.post("/instances", response_model=dict)
async def create_dify_instance(
    config: DifyInstanceConfig,
    dify_service: DifyService = Depends(get_dify_service)
):
    """创建Dify实例"""
    try:
        instance_id = await dify_service.create_instance(config)
        return {
            "success": True,
            "data": {"instance_id": instance_id},
            "message": "Dify实例创建成功"
        }
    except Exception as e:
        logger.error(f"创建Dify实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@router.get("/instances", response_model=DifyInstanceListResponse)
async def list_dify_instances(
    enabled_only: bool = Query(False, description="仅显示启用的实例"),
    dify_service: DifyService = Depends(get_dify_service)
):
    """获取Dify实例列表"""
    try:
        instances = await dify_service.list_instances(enabled_only=enabled_only)
        return DifyInstanceListResponse(
            data=instances,
            total=len(instances)
        )
    except Exception as e:
        logger.error(f"获取Dify实例列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/instances/{instance_id}", response_model=dict)
async def get_dify_instance(
    instance_id: str,
    dify_service: DifyService = Depends(get_dify_service)
):
    """获取Dify实例详情"""
    try:
        instance = await dify_service.get_instance(instance_id)
        if not instance:
            raise HTTPException(status_code=404, detail="实例不存在")
        
        return {
            "success": True,
            "data": instance,
            "message": "获取成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Dify实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.put("/instances/{instance_id}", response_model=dict)
async def update_dify_instance(
    instance_id: str,
    config: DifyInstanceConfig,
    dify_service: DifyService = Depends(get_dify_service)
):
    """更新Dify实例"""
    try:
        success = await dify_service.update_instance(instance_id, config)
        if not success:
            raise HTTPException(status_code=404, detail="实例不存在")
        
        return {
            "success": True,
            "message": "更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新Dify实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")


@router.delete("/instances/{instance_id}", response_model=dict)
async def delete_dify_instance(
    instance_id: str,
    dify_service: DifyService = Depends(get_dify_service)
):
    """删除Dify实例"""
    try:
        success = await dify_service.delete_instance(instance_id)
        if not success:
            raise HTTPException(status_code=404, detail="实例不存在")
        
        return {
            "success": True,
            "message": "删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除Dify实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.post("/instances/test", response_model=DifyTestResponse)
async def test_dify_instance(
    request: DifyTestRequest,
    dify_service: DifyService = Depends(get_dify_service)
):
    """测试Dify实例连接"""
    try:
        return await dify_service.test_instance(request)
    except Exception as e:
        logger.error(f"测试Dify实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")


# 工作流配置管理
@router.post("/instances/{instance_id}/workflows", response_model=dict)
async def create_workflow_config(
    instance_id: str,
    config: DifyWorkflowConfig,
    dify_service: DifyService = Depends(get_dify_service)
):
    """创建工作流配置"""
    try:
        workflow_id = await dify_service.create_workflow(instance_id, config)
        return {
            "success": True,
            "data": {"workflow_id": workflow_id},
            "message": "工作流配置创建成功"
        }
    except Exception as e:
        logger.error(f"创建工作流配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@router.get("/workflows", response_model=DifyWorkflowListResponse)
async def list_workflow_configs(
    instance_id: Optional[str] = Query(None, description="实例ID"),
    enabled_only: bool = Query(False, description="仅显示启用的工作流"),
    dify_service: DifyService = Depends(get_dify_service)
):
    """获取工作流配置列表"""
    try:
        workflows = await dify_service.list_workflows(
            instance_id=instance_id,
            enabled_only=enabled_only
        )
        return DifyWorkflowListResponse(
            data=workflows,
            total=len(workflows)
        )
    except Exception as e:
        logger.error(f"获取工作流配置列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/workflows/{workflow_id}", response_model=dict)
async def get_workflow_config(
    workflow_id: str,
    dify_service: DifyService = Depends(get_dify_service)
):
    """获取工作流配置详情"""
    try:
        workflow = await dify_service.get_workflow(workflow_id)
        if not workflow:
            raise HTTPException(status_code=404, detail="工作流配置不存在")
        
        return {
            "success": True,
            "data": workflow,
            "message": "获取成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工作流配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.put("/workflows/{workflow_id}", response_model=dict)
async def update_workflow_config(
    workflow_id: str,
    config: DifyWorkflowConfig,
    dify_service: DifyService = Depends(get_dify_service)
):
    """更新工作流配置"""
    try:
        success = await dify_service.update_workflow(workflow_id, config)
        if not success:
            raise HTTPException(status_code=404, detail="工作流配置不存在")
        
        return {
            "success": True,
            "message": "更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新工作流配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")


@router.delete("/workflows/{workflow_id}", response_model=dict)
async def delete_workflow_config(
    workflow_id: str,
    dify_service: DifyService = Depends(get_dify_service)
):
    """删除工作流配置"""
    try:
        success = await dify_service.delete_workflow(workflow_id)
        if not success:
            raise HTTPException(status_code=404, detail="工作流配置不存在")
        
        return {
            "success": True,
            "message": "删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除工作流配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


# 工作流执行
@router.post("/execute", response_model=DifyExecutionResponse)
async def execute_dify_workflow(
    request: DifyExecutionRequest,
    dify_service: DifyService = Depends(get_dify_service)
):
    """执行Dify工作流"""
    try:
        return await dify_service.execute_workflow(
            request.instance_id,
            request.workflow_id,
            request
        )
    except Exception as e:
        logger.error(f"执行Dify工作流失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"执行失败: {str(e)}")


# 执行记录查询
@router.get("/executions", response_model=DifyExecutionListResponse)
async def list_execution_logs(
    instance_id: Optional[str] = Query(None, description="实例ID"),
    workflow_id: Optional[str] = Query(None, description="工作流ID"),
    task_id: Optional[str] = Query(None, description="任务ID"),
    limit: int = Query(100, description="返回数量限制"),
    dify_service: DifyService = Depends(get_dify_service)
):
    """获取执行记录列表"""
    try:
        logs = await dify_service.list_execution_logs(
            instance_id=instance_id,
            workflow_id=workflow_id,
            task_id=task_id,
            limit=limit
        )
        return DifyExecutionListResponse(
            data=logs,
            total=len(logs)
        )
    except Exception as e:
        logger.error(f"获取执行记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/executions/{execution_id}", response_model=dict)
async def get_execution_log(
    execution_id: str,
    dify_service: DifyService = Depends(get_dify_service)
):
    """获取执行记录详情"""
    try:
        log = await dify_service.get_execution_log(execution_id)
        if not log:
            raise HTTPException(status_code=404, detail="执行记录不存在")
        
        return {
            "success": True,
            "data": log,
            "message": "获取成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


# 工作流模板管理
@router.get("/workflow-templates")
async def get_workflow_templates():
    """获取可用的工作流模板列表"""
    try:
        import os
        import yaml
        from pathlib import Path

        templates = []

        # 扫描工作流配置目录
        workflow_dirs = [
            "core/config/workflows",
            "core/config/platforms/youtube/workflows"
        ]

        for workflow_dir in workflow_dirs:
            if os.path.exists(workflow_dir):
                for file_path in Path(workflow_dir).glob("*.yaml"):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            config = yaml.safe_load(f)

                        workflow_info = config.get('workflow', {})
                        metadata = config.get('metadata', {})

                        # 检查是否包含Dify步骤
                        has_dify_steps = False
                        steps = workflow_info.get('steps', [])
                        for step in steps:
                            if step.get('action') == 'dify_workflow':
                                has_dify_steps = True
                                break

                        template = {
                            "id": file_path.stem,
                            "name": workflow_info.get('name', file_path.stem),
                            "description": workflow_info.get('description', ''),
                            "version": workflow_info.get('version', '1.0'),
                            "file_path": str(file_path),
                            "has_dify_integration": has_dify_steps,
                            "tags": metadata.get('tags', []),
                            "use_cases": metadata.get('use_cases', []),
                            "required_inputs": metadata.get('required_inputs', []),
                            "expected_outputs": metadata.get('expected_outputs', []),
                            "step_count": len(steps)
                        }

                        templates.append(template)

                    except Exception as e:
                        logger.warning(f"解析工作流模板失败 {file_path}: {str(e)}")
                        continue

        return {
            "success": True,
            "data": templates,
            "total": len(templates)
        }

    except Exception as e:
        logger.error(f"获取工作流模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflow-templates/{template_id}")
async def get_workflow_template_detail(template_id: str):
    """获取工作流模板详情"""
    try:
        import os
        import yaml
        from pathlib import Path

        # 查找模板文件
        workflow_dirs = [
            "core/config/workflows",
            "core/config/platforms/youtube/workflows"
        ]

        template_file = None
        for workflow_dir in workflow_dirs:
            potential_path = Path(workflow_dir) / f"{template_id}.yaml"
            if potential_path.exists():
                template_file = potential_path
                break

        if not template_file:
            raise HTTPException(status_code=404, detail="工作流模板不存在")

        with open(template_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        return {
            "success": True,
            "data": config
        }

    except Exception as e:
        logger.error(f"获取工作流模板详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
