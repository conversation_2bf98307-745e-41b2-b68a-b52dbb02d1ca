<template>
  <el-dialog
    v-model="visible"
    title="执行详情"
    width="900px"
    :close-on-click-modal="false"
  >
    <div v-if="execution" class="execution-detail">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(execution.status)">
              {{ getStatusText(execution.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行ID">
            <el-text class="execution-id">{{ execution.execution_id }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="任务ID">
            <el-text>{{ execution.task_id || '-' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="实例ID">
            <el-text>{{ execution.instance_id }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="工作流ID">
            <el-text>{{ execution.workflow_id }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            <el-text>{{ formatTime(execution.started_at) }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            <el-text>{{ execution.completed_at ? formatTime(execution.completed_at) : '-' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="执行耗时">
            <el-text>{{ execution.duration ? `${execution.duration.toFixed(2)}秒` : '-' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="重试次数">
            <el-text>{{ execution.retry_count || 0 }}次</el-text>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 错误信息 -->
      <el-card 
        v-if="execution.error_message" 
        class="error-card" 
        shadow="never"
      >
        <template #header>
          <div class="card-header">
            <span>错误信息</span>
            <el-tag type="danger" v-if="execution.error_code">
              {{ execution.error_code }}
            </el-tag>
          </div>
        </template>
        
        <el-alert
          :title="execution.error_message"
          type="error"
          :closable="false"
          show-icon
        />
      </el-card>

      <!-- 请求数据 -->
      <el-card class="data-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>请求数据</span>
            <el-button 
              size="small" 
              @click="copyToClipboard(JSON.stringify(execution.request_data, null, 2))"
            >
              复制
            </el-button>
          </div>
        </template>
        
        <div class="json-container">
          <pre class="json-content">{{ formatJson(execution.request_data) }}</pre>
        </div>
      </el-card>

      <!-- 响应数据 -->
      <el-card 
        v-if="execution.response_data" 
        class="data-card" 
        shadow="never"
      >
        <template #header>
          <div class="card-header">
            <span>响应数据</span>
            <el-button 
              size="small" 
              @click="copyToClipboard(JSON.stringify(execution.response_data, null, 2))"
            >
              复制
            </el-button>
          </div>
        </template>
        
        <div class="json-container">
          <pre class="json-content">{{ formatJson(execution.response_data) }}</pre>
        </div>
      </el-card>

      <!-- 使用统计 -->
      <el-card 
        v-if="execution.token_usage || execution.cost" 
        class="stats-card" 
        shadow="never"
      >
        <template #header>
          <span>使用统计</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12" v-if="execution.token_usage">
            <div class="stat-item">
              <h4>Token使用量</h4>
              <el-descriptions :column="1" size="small">
                <el-descriptions-item 
                  v-for="(value, key) in execution.token_usage" 
                  :key="key"
                  :label="formatTokenKey(key)"
                >
                  {{ value }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
          <el-col :span="12" v-if="execution.cost">
            <div class="stat-item">
              <h4>费用</h4>
              <div class="cost-display">
                <span class="cost-amount">${{ execution.cost.toFixed(4) }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 元数据 -->
      <el-card 
        v-if="execution.metadata && Object.keys(execution.metadata).length > 0" 
        class="data-card" 
        shadow="never"
      >
        <template #header>
          <div class="card-header">
            <span>元数据</span>
            <el-button 
              size="small" 
              @click="copyToClipboard(JSON.stringify(execution.metadata, null, 2))"
            >
              复制
            </el-button>
          </div>
        </template>
        
        <div class="json-container">
          <pre class="json-content">{{ formatJson(execution.metadata) }}</pre>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { DifyExecutionLog } from '@/api/dify'

// Props
interface Props {
  modelValue: boolean
  execution?: DifyExecutionLog | null
}

const props = withDefaults(defineProps<Props>(), {
  execution: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 工具函数
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatJson = (data: any) => {
  if (!data) return ''
  try {
    return JSON.stringify(data, null, 2)
  } catch {
    return String(data)
  }
}

const formatTokenKey = (key: string) => {
  const keyMap: Record<string, string> = {
    prompt_tokens: '提示词Token',
    completion_tokens: '完成Token',
    total_tokens: '总Token',
    input_tokens: '输入Token',
    output_tokens: '输出Token'
  }
  return keyMap[key] || key
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.execution-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card,
.error-card,
.data-card,
.stats-card {
  margin-bottom: 16px;
}

.info-card:last-child,
.error-card:last-child,
.data-card:last-child,
.stats-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.execution-id {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.json-container {
  max-height: 300px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}

.json-content {
  margin: 0;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #2c3e50;
  white-space: pre-wrap;
  word-break: break-word;
}

.stat-item h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.cost-display {
  text-align: center;
  padding: 20px;
}

.cost-amount {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-alert) {
  margin: 0;
}

:deep(.el-alert .el-alert__content) {
  word-break: break-word;
}
</style>
