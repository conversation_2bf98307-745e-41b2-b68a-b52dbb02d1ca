"""
Dify工作流服务
提供高级的Dify工作流管理和执行功能
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId

from app.core.schemas.dify_models import (
    DifyInstanceConfig,
    DifyWorkflowConfig,
    DifyExecutionRequest,
    DifyExecutionResponse,
    DifyExecutionLog,
    DifyExecutionStatus,
    DifyTestRequest,
    DifyTestResponse,
    DifyInstanceStatus
)
from app.services.dify_client import DifyClient, DifyAPIError

logger = logging.getLogger(__name__)


class DifyService:
    """Dify工作流服务"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.instances_collection = db.dify_instances
        self.workflows_collection = db.dify_workflows
        self.executions_collection = db.dify_executions
        self._client_cache: Dict[str, DifyClient] = {}
    
    async def create_instance(self, config: DifyInstanceConfig) -> str:
        """创建Dify实例"""
        try:
            # 设置创建时间
            config.created_at = datetime.now()
            config.updated_at = datetime.now()
            
            # 插入数据库
            result = await self.instances_collection.insert_one(config.dict(exclude={'id'}))
            instance_id = str(result.inserted_id)
            
            logger.info(f"创建Dify实例成功: {config.name} ({instance_id})")
            return instance_id
            
        except Exception as e:
            logger.error(f"创建Dify实例失败: {str(e)}")
            raise
    
    async def update_instance(self, instance_id: str, config: DifyInstanceConfig) -> bool:
        """更新Dify实例"""
        try:
            config.updated_at = datetime.now()
            
            result = await self.instances_collection.update_one(
                {"_id": ObjectId(instance_id)},
                {"$set": config.dict(exclude={'id', 'created_at'})}
            )
            
            if result.modified_count > 0:
                # 清除缓存的客户端
                if instance_id in self._client_cache:
                    await self._client_cache[instance_id].close()
                    del self._client_cache[instance_id]
                
                logger.info(f"更新Dify实例成功: {instance_id}")
                return True
            else:
                logger.warning(f"Dify实例不存在或无变化: {instance_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新Dify实例失败: {str(e)}")
            raise
    
    async def delete_instance(self, instance_id: str) -> bool:
        """删除Dify实例"""
        try:
            # 清除缓存的客户端
            if instance_id in self._client_cache:
                await self._client_cache[instance_id].close()
                del self._client_cache[instance_id]
            
            # 删除相关的工作流配置
            await self.workflows_collection.delete_many({"instance_id": instance_id})
            
            # 删除实例
            result = await self.instances_collection.delete_one({"_id": ObjectId(instance_id)})
            
            if result.deleted_count > 0:
                logger.info(f"删除Dify实例成功: {instance_id}")
                return True
            else:
                logger.warning(f"Dify实例不存在: {instance_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除Dify实例失败: {str(e)}")
            raise
    
    async def get_instance(self, instance_id: str) -> Optional[DifyInstanceConfig]:
        """获取Dify实例"""
        try:
            doc = await self.instances_collection.find_one({"_id": ObjectId(instance_id)})
            if doc:
                doc['id'] = str(doc['_id'])
                doc.pop('_id')
                return DifyInstanceConfig(**doc)
            return None
            
        except Exception as e:
            logger.error(f"获取Dify实例失败: {str(e)}")
            return None
    
    async def list_instances(self, enabled_only: bool = False) -> List[DifyInstanceConfig]:
        """获取Dify实例列表"""
        try:
            query = {}
            if enabled_only:
                query['enabled'] = True
            
            cursor = self.instances_collection.find(query).sort("created_at", -1)
            instances = []
            
            async for doc in cursor:
                doc['id'] = str(doc['_id'])
                doc.pop('_id')
                instances.append(DifyInstanceConfig(**doc))
            
            return instances
            
        except Exception as e:
            logger.error(f"获取Dify实例列表失败: {str(e)}")
            return []
    
    async def test_instance(self, request: DifyTestRequest) -> DifyTestResponse:
        """测试Dify实例连接"""
        try:
            if request.instance_id:
                # 测试已保存的实例
                instance = await self.get_instance(request.instance_id)
                if not instance:
                    return DifyTestResponse(
                        success=False,
                        message="实例不存在",
                        tested_at=datetime.now()
                    )
                
                client = await self._get_client(instance)
                result = await client.test_connection()
                
                # 更新实例状态
                await self._update_instance_status(
                    request.instance_id,
                    DifyInstanceStatus.ACTIVE if result.success else DifyInstanceStatus.ERROR,
                    None if result.success else result.error_details
                )
                
                return result
            else:
                # 测试新配置
                if not request.base_url or not request.api_key:
                    return DifyTestResponse(
                        success=False,
                        message="缺少必需的配置参数",
                        tested_at=datetime.now()
                    )
                
                temp_config = DifyInstanceConfig(
                    name="test",
                    base_url=request.base_url,
                    api_key=request.api_key,
                    timeout=request.timeout
                )
                
                async with DifyClient(temp_config) as client:
                    return await client.test_connection()
                    
        except Exception as e:
            logger.error(f"测试Dify实例失败: {str(e)}")
            return DifyTestResponse(
                success=False,
                message=f"测试失败: {str(e)}",
                error_details=str(e),
                tested_at=datetime.now()
            )
    
    async def execute_workflow(
        self,
        instance_id: str,
        workflow_id: str,
        request: DifyExecutionRequest
    ) -> DifyExecutionResponse:
        """执行Dify工作流"""
        execution_id = str(uuid.uuid4())
        
        try:
            # 获取实例配置
            instance = await self.get_instance(instance_id)
            if not instance:
                raise ValueError(f"Dify实例不存在: {instance_id}")
            
            if not instance.enabled:
                raise ValueError(f"Dify实例已禁用: {instance_id}")
            
            # 获取工作流配置
            workflow = await self._get_workflow_config(instance_id, workflow_id)
            if not workflow:
                raise ValueError(f"工作流配置不存在: {workflow_id}")
            
            if not workflow.enabled:
                raise ValueError(f"工作流已禁用: {workflow_id}")
            
            # 创建执行日志
            log = DifyExecutionLog(
                execution_id=execution_id,
                task_id=request.task_id,
                instance_id=instance_id,
                workflow_id=workflow_id,
                status=DifyExecutionStatus.RUNNING,
                request_data=request.dict(),
                started_at=datetime.now(),
                created_at=datetime.now()
            )
            
            await self.executions_collection.insert_one(log.dict(exclude={'id'}))
            
            # 获取客户端并执行
            client = await self._get_client(instance)
            response = await client.execute_workflow(workflow, request)
            
            # 更新执行日志
            log.status = DifyExecutionStatus.COMPLETED if response.success else DifyExecutionStatus.FAILED
            log.response_data = response.dict()
            log.completed_at = response.completed_at
            log.duration = response.duration
            log.error_message = response.error_message
            log.token_usage = response.token_usage
            log.cost = response.cost
            
            await self.executions_collection.update_one(
                {"execution_id": execution_id},
                {"$set": log.dict(exclude={'id', 'created_at'})}
            )
            
            # 设置执行ID
            response.execution_id = execution_id
            
            logger.info(f"Dify工作流执行完成: {execution_id} - {'成功' if response.success else '失败'}")
            return response
            
        except Exception as e:
            # 更新执行日志为失败状态
            await self.executions_collection.update_one(
                {"execution_id": execution_id},
                {
                    "$set": {
                        "status": DifyExecutionStatus.FAILED,
                        "error_message": str(e),
                        "completed_at": datetime.now()
                    }
                }
            )
            
            logger.error(f"Dify工作流执行失败: {execution_id} - {str(e)}")
            
            return DifyExecutionResponse(
                success=False,
                execution_id=execution_id,
                status=DifyExecutionStatus.FAILED,
                error_message=str(e),
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
    
    async def get_execution_log(self, execution_id: str) -> Optional[DifyExecutionLog]:
        """获取执行日志"""
        try:
            doc = await self.executions_collection.find_one({"execution_id": execution_id})
            if doc:
                doc['id'] = str(doc['_id'])
                doc.pop('_id')
                return DifyExecutionLog(**doc)
            return None
            
        except Exception as e:
            logger.error(f"获取执行日志失败: {str(e)}")
            return None
    
    async def list_execution_logs(
        self,
        instance_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        task_id: Optional[str] = None,
        limit: int = 100
    ) -> List[DifyExecutionLog]:
        """获取执行日志列表"""
        try:
            query = {}
            if instance_id:
                query['instance_id'] = instance_id
            if workflow_id:
                query['workflow_id'] = workflow_id
            if task_id:
                query['task_id'] = task_id
            
            cursor = self.executions_collection.find(query).sort("started_at", -1).limit(limit)
            logs = []
            
            async for doc in cursor:
                doc['id'] = str(doc['_id'])
                doc.pop('_id')
                logs.append(DifyExecutionLog(**doc))
            
            return logs
            
        except Exception as e:
            logger.error(f"获取执行日志列表失败: {str(e)}")
            return []
    
    async def _get_client(self, instance: DifyInstanceConfig) -> DifyClient:
        """获取或创建Dify客户端"""
        instance_id = instance.id
        
        if instance_id not in self._client_cache:
            self._client_cache[instance_id] = DifyClient(instance)
            await self._client_cache[instance_id].connect()
        
        return self._client_cache[instance_id]
    
    async def _get_workflow_config(self, instance_id: str, workflow_id: str) -> Optional[DifyWorkflowConfig]:
        """获取工作流配置"""
        try:
            doc = await self.workflows_collection.find_one({
                "instance_id": instance_id,
                "workflow_id": workflow_id
            })
            
            if doc:
                doc['id'] = str(doc['_id'])
                doc.pop('_id')
                return DifyWorkflowConfig(**doc)
            return None
            
        except Exception as e:
            logger.error(f"获取工作流配置失败: {str(e)}")
            return None
    
    async def _update_instance_status(
        self,
        instance_id: str,
        status: DifyInstanceStatus,
        error_message: Optional[str] = None
    ):
        """更新实例状态"""
        try:
            update_data = {
                "status": status,
                "last_test_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            if error_message:
                update_data["last_error"] = error_message
            else:
                update_data["last_error"] = None
            
            await self.instances_collection.update_one(
                {"_id": ObjectId(instance_id)},
                {"$set": update_data}
            )
            
        except Exception as e:
            logger.error(f"更新实例状态失败: {str(e)}")
    
    async def create_workflow(self, instance_id: str, config: DifyWorkflowConfig) -> str:
        """创建工作流配置"""
        try:
            # 验证实例存在
            instance = await self.get_instance(instance_id)
            if not instance:
                raise ValueError(f"Dify实例不存在: {instance_id}")

            # 设置时间和实例ID
            config.created_at = datetime.now()
            config.updated_at = datetime.now()

            # 插入数据库
            workflow_data = config.dict(exclude={'id'})
            workflow_data['instance_id'] = instance_id

            result = await self.workflows_collection.insert_one(workflow_data)
            workflow_config_id = str(result.inserted_id)

            logger.info(f"创建工作流配置成功: {config.name} ({workflow_config_id})")
            return workflow_config_id

        except Exception as e:
            logger.error(f"创建工作流配置失败: {str(e)}")
            raise

    async def update_workflow(self, workflow_config_id: str, config: DifyWorkflowConfig) -> bool:
        """更新工作流配置"""
        try:
            config.updated_at = datetime.now()

            result = await self.workflows_collection.update_one(
                {"_id": ObjectId(workflow_config_id)},
                {"$set": config.dict(exclude={'id', 'created_at'})}
            )

            if result.modified_count > 0:
                logger.info(f"更新工作流配置成功: {workflow_config_id}")
                return True
            else:
                logger.warning(f"工作流配置不存在或无变化: {workflow_config_id}")
                return False

        except Exception as e:
            logger.error(f"更新工作流配置失败: {str(e)}")
            raise

    async def delete_workflow(self, workflow_config_id: str) -> bool:
        """删除工作流配置"""
        try:
            result = await self.workflows_collection.delete_one({"_id": ObjectId(workflow_config_id)})

            if result.deleted_count > 0:
                logger.info(f"删除工作流配置成功: {workflow_config_id}")
                return True
            else:
                logger.warning(f"工作流配置不存在: {workflow_config_id}")
                return False

        except Exception as e:
            logger.error(f"删除工作流配置失败: {str(e)}")
            raise

    async def get_workflow(self, workflow_config_id: str) -> Optional[DifyWorkflowConfig]:
        """获取工作流配置"""
        try:
            doc = await self.workflows_collection.find_one({"_id": ObjectId(workflow_config_id)})
            if doc:
                doc['id'] = str(doc['_id'])
                doc.pop('_id')
                doc.pop('instance_id', None)  # 移除内部字段
                return DifyWorkflowConfig(**doc)
            return None

        except Exception as e:
            logger.error(f"获取工作流配置失败: {str(e)}")
            return None

    async def list_workflows(self, instance_id: Optional[str] = None, enabled_only: bool = False) -> List[DifyWorkflowConfig]:
        """获取工作流配置列表"""
        try:
            query = {}
            if instance_id:
                query['instance_id'] = instance_id
            if enabled_only:
                query['enabled'] = True

            cursor = self.workflows_collection.find(query).sort("created_at", -1)
            workflows = []

            async for doc in cursor:
                doc['id'] = str(doc['_id'])
                doc.pop('_id')
                doc.pop('instance_id', None)  # 移除内部字段
                workflows.append(DifyWorkflowConfig(**doc))

            return workflows

        except Exception as e:
            logger.error(f"获取工作流配置列表失败: {str(e)}")
            return []

    async def close(self):
        """关闭服务，清理资源"""
        for client in self._client_cache.values():
            await client.close()
        self._client_cache.clear()
        logger.info("Dify服务已关闭")
