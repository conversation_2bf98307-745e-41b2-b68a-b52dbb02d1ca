# YouTube AI增强视频上传工作流配置
# 集成Dify AI自动生成标题、描述和标签

workflow:
  name: "YouTube AI增强视频上传"
  description: "使用Dify AI自动生成内容的YouTube视频上传流程"
  version: "1.0"
  
  # 工作流步骤
  steps:
    # AI内容生成阶段
    - name: "AI生成视频标题"
      id: "ai_generate_title"
      description: "使用Dify AI根据视频内容生成吸引人的标题"
      action: "dify_workflow"
      required: true
      timeout: 60
      parameters:
        instance_id: "content_generator"
        workflow_id: "youtube_title_generator"
        inputs:
          video_topic: "${video_topic}"
          target_keywords: "${target_keywords}"
          video_duration: "${video_duration}"
          content_type: "youtube_video"
        output_mappings:
          title: "ai_generated_title"
          title_alternatives: "title_options"
      notes: "生成SEO优化的YouTube视频标题"

    - name: "AI生成视频描述"
      id: "ai_generate_description"
      description: "基于标题和内容生成详细的视频描述"
      action: "dify_workflow"
      required: true
      timeout: 90
      parameters:
        instance_id: "content_generator"
        workflow_id: "youtube_description_generator"
        inputs:
          video_title: "${ai_generated_title}"
          video_topic: "${video_topic}"
          video_highlights: "${video_highlights}"
          channel_info: "${channel_info}"
          call_to_action: "请点赞、订阅并开启小铃铛！"
        output_mappings:
          description: "ai_generated_description"
          hashtags: "ai_generated_hashtags"
      notes: "生成包含关键词和CTA的视频描述"

    - name: "AI生成视频标签"
      id: "ai_generate_tags"
      description: "为视频生成相关标签提高搜索可见性"
      action: "dify_workflow"
      required: false
      timeout: 45
      parameters:
        instance_id: "content_generator"
        workflow_id: "youtube_tags_generator"
        inputs:
          video_title: "${ai_generated_title}"
          video_description: "${ai_generated_description}"
          video_category: "${video_category}"
        output_mappings:
          tags: "ai_generated_tags"
      notes: "生成SEO友好的视频标签"

    # YouTube上传操作阶段
    - name: "点击创建按钮"
      id: "click_create"
      description: "点击YouTube主界面的创建按钮（+按钮）"
      action: "click"
      element: "create_button"
      wait_after: 2
      required: true
      
    - name: "选择上传视频选项"
      id: "select_upload"
      description: "在创建选项中选择上传视频"
      action: "click"
      element: "video_upload_option"
      wait_after: 2
      required: true
      
    - name: "选择视频文件"
      id: "select_video"
      description: "选择要上传的视频文件"
      action: "click"
      element: "video_file"
      wait_after: 3
      required: true
      parameters:
        filename: "${video_filename}"
          
    - name: "输入AI生成的标题"
      id: "input_ai_title"
      description: "输入AI生成的视频标题"
      action: "input_text"
      element: "title_input"
      wait_after: 1
      required: true
      parameters:
        text: "${ai_generated_title}"
          
    - name: "输入AI生成的描述"
      id: "input_ai_description"
      description: "输入AI生成的视频描述"
      action: "input_text"
      element: "description_input"
      wait_after: 1
      required: true
      parameters:
        text: "${ai_generated_description}"

    - name: "选择视频缩略图"
      id: "select_thumbnail"
      description: "选择或上传视频缩略图"
      action: "click"
      element: "thumbnail_option"
      wait_after: 2
      required: false
      parameters:
        thumbnail_path: "${thumbnail_path}"

    - name: "设置视频可见性"
      id: "set_visibility"
      description: "设置视频的可见性（公开/不公开/私人）"
      action: "select_option"
      element: "visibility_dropdown"
      wait_after: 1
      required: true
      parameters:
        option: "${video_visibility}"

    - name: "添加AI生成的标签"
      id: "add_ai_tags"
      description: "添加AI生成的视频标签"
      action: "input_text"
      element: "tags_input"
      wait_after: 1
      required: false
      parameters:
        text: "${ai_generated_tags}"

    - name: "选择视频分类"
      id: "select_category"
      description: "选择视频分类"
      action: "select_option"
      element: "category_dropdown"
      wait_after: 1
      required: false
      parameters:
        option: "${video_category}"

    - name: "设置视频语言"
      id: "set_language"
      description: "设置视频语言"
      action: "select_option"
      element: "language_dropdown"
      wait_after: 1
      required: false
      parameters:
        option: "${video_language}"

    - name: "发布视频"
      id: "publish_video"
      description: "点击发布按钮完成视频上传"
      action: "click"
      element: "publish_button"
      wait_after: 5
      required: true

    - name: "等待发布完成"
      id: "wait_publish_complete"
      description: "等待视频发布处理完成"
      action: "wait_for_element"
      element: "publish_success_indicator"
      timeout: 300
      required: true

# 工作流配置
config:
  # 默认参数
  default_parameters:
    video_visibility: "public"
    video_language: "zh-CN"
    video_category: "Entertainment"
    channel_info: "欢迎来到我们的频道！"
    
  # AI配置
  ai_config:
    content_generator_instance: "content_generator"
    fallback_to_manual: true  # AI失败时回退到手动输入
    
  # 重试配置
  retry_config:
    max_retries: 3
    retry_delay: 5
    
  # 超时配置
  timeout_config:
    ai_step_timeout: 90
    upload_step_timeout: 30
    publish_timeout: 300

# 工作流元数据
metadata:
  tags:
    - "YouTube上传"
    - "AI内容生成"
    - "Dify集成"
    - "自动化"
  
  required_inputs:
    - video_filename: "要上传的视频文件名"
    - video_topic: "视频主题"
    - target_keywords: "目标关键词"
    - video_duration: "视频时长（秒）"
    - video_highlights: "视频亮点"
  
  optional_inputs:
    - thumbnail_path: "缩略图文件路径"
    - video_category: "视频分类"
    - video_visibility: "视频可见性"
    - video_language: "视频语言"
  
  ai_generated_outputs:
    - ai_generated_title: "AI生成的视频标题"
    - ai_generated_description: "AI生成的视频描述"
    - ai_generated_tags: "AI生成的视频标签"
    - ai_generated_hashtags: "AI生成的话题标签"

# 错误处理和回退策略
error_handling:
  ai_failure_fallback:
    - step: "ai_generate_title"
      fallback_action: "use_manual_title"
      fallback_value: "${manual_title}"
    - step: "ai_generate_description"
      fallback_action: "use_manual_description"
      fallback_value: "${manual_description}"
    - step: "ai_generate_tags"
      fallback_action: "use_manual_tags"
      fallback_value: "${manual_tags}"
