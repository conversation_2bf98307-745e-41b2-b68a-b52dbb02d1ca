# Dify内容生成工作流示例
# 展示如何在工作流中集成Dify AI功能

workflow:
  name: "AI内容生成工作流"
  description: "使用Dify AI生成视频标题、描述和标签"
  version: "1.0.0"
  author: "ThunderHub"
  
  # 工作流步骤
  steps:
    # 步骤1: 生成视频标题
    - id: "generate_title"
      name: "生成视频标题"
      description: "使用Dify AI根据视频内容生成吸引人的标题"
      action: "dify_workflow"
      required: true
      timeout: 60
      parameters:
        instance_id: "default_dify_instance"  # Dify实例ID
        workflow_id: "title_generator"        # Dify工作流ID
        inputs:
          video_topic: "${video_topic}"       # 从上下文获取视频主题
          target_audience: "${target_audience}" # 目标受众
          video_duration: "${video_duration}" # 视频时长
          keywords: "${keywords}"             # 关键词
        output_mappings:
          title: "generated_title"            # 将Dify输出的title映射到上下文的generated_title
          title_alternatives: "title_options" # 备选标题
      notes: "生成的标题将保存到工作流上下文中，供后续步骤使用"

    # 步骤2: 生成视频描述
    - id: "generate_description"
      name: "生成视频描述"
      description: "基于生成的标题创建详细的视频描述"
      action: "dify_workflow"
      required: true
      timeout: 90
      parameters:
        instance_id: "default_dify_instance"
        workflow_id: "description_generator"
        inputs:
          video_title: "${generated_title}"   # 使用上一步生成的标题
          video_topic: "${video_topic}"
          video_content: "${video_content}"   # 视频内容摘要
          call_to_action: "${call_to_action}" # 行动号召
        output_mappings:
          description: "generated_description"
          hashtags: "generated_hashtags"
      notes: "生成包含关键词和行动号召的视频描述"

    # 步骤3: 生成标签和分类
    - id: "generate_tags"
      name: "生成标签和分类"
      description: "为视频生成相关标签和分类建议"
      action: "dify_workflow"
      required: false  # 可选步骤
      timeout: 45
      parameters:
        instance_id: "default_dify_instance"
        workflow_id: "tags_generator"
        inputs:
          video_title: "${generated_title}"
          video_description: "${generated_description}"
          video_category: "${video_category}"
        output_mappings:
          tags: "generated_tags"
          category_suggestions: "category_suggestions"
      notes: "生成SEO友好的标签和分类建议"

    # 步骤4: 内容质量检查
    - id: "content_quality_check"
      name: "内容质量检查"
      description: "检查生成内容的质量和合规性"
      action: "dify_workflow"
      required: true
      timeout: 30
      parameters:
        instance_id: "default_dify_instance"
        workflow_id: "content_checker"
        inputs:
          title: "${generated_title}"
          description: "${generated_description}"
          tags: "${generated_tags}"
          platform_guidelines: "${platform_guidelines}"
        output_mappings:
          quality_score: "content_quality_score"
          compliance_check: "compliance_result"
          suggestions: "improvement_suggestions"
      notes: "确保内容符合平台规范和质量标准"

    # 步骤5: 生成缩略图文案
    - id: "generate_thumbnail_text"
      name: "生成缩略图文案"
      description: "为视频缩略图生成吸引人的文案"
      action: "dify_workflow"
      required: false
      timeout: 30
      parameters:
        instance_id: "default_dify_instance"
        workflow_id: "thumbnail_text_generator"
        inputs:
          video_title: "${generated_title}"
          video_topic: "${video_topic}"
          thumbnail_style: "${thumbnail_style}"
        output_mappings:
          thumbnail_text: "thumbnail_text"
          text_style: "thumbnail_text_style"
      notes: "生成适合缩略图的简短有力文案"

# 工作流配置
config:
  # 默认参数
  default_parameters:
    target_audience: "18-35岁年轻人"
    platform_guidelines: "YouTube社区准则"
    thumbnail_style: "简洁明了"
    
  # 重试配置
  retry_config:
    max_retries: 3
    retry_delay: 5
    
  # 超时配置
  timeout_config:
    default_step_timeout: 60
    max_workflow_timeout: 600
    
  # 错误处理
  error_handling:
    continue_on_optional_failure: true
    save_partial_results: true
    
  # 输出配置
  output_config:
    save_to_database: true
    export_format: "json"
    include_metadata: true

# 工作流元数据
metadata:
  tags:
    - "AI内容生成"
    - "Dify集成"
    - "自动化"
    - "内容创作"
  
  use_cases:
    - "YouTube视频内容生成"
    - "社交媒体内容创作"
    - "批量内容生成"
    - "内容质量控制"
  
  requirements:
    - "已配置的Dify实例"
    - "相应的Dify工作流"
    - "有效的API密钥"
  
  expected_inputs:
    - video_topic: "视频主题"
    - video_content: "视频内容摘要"
    - video_duration: "视频时长（秒）"
    - keywords: "关键词列表"
    - video_category: "视频分类"
    - call_to_action: "行动号召文案"
  
  expected_outputs:
    - generated_title: "生成的视频标题"
    - generated_description: "生成的视频描述"
    - generated_tags: "生成的标签列表"
    - generated_hashtags: "生成的话题标签"
    - content_quality_score: "内容质量评分"
    - thumbnail_text: "缩略图文案"
